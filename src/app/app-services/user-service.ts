import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable, catchError, map, of, switchMap, throwError } from 'rxjs';
import { Utility } from "../shared/utility/utility";
import { ReplaySubject } from 'rxjs';
import { environment } from 'src/environments/environment.prod';

@Injectable({ providedIn: 'root' })

export class UserService {
  imageApiUrl: any = "rewards/upload-product-image";
  loginURL: any = "auth/signin";
  percentageGrowthBaseURL: any = "rewards-points/";
  getAllTerritoriesDataURL: any = "territories/";
  statusURL: any = 'customers/toggle-status';
  usersBaseURL: any = "users/byRole";
  usersRoleBaseURL: any = "users/all";
  distributorsBaseURL: any = "distributor/all";
  retailersBaseURL: any = "retailers/all";
  farmersBaseURL: any = "farmer/all";
  sbusBaseURL: any = "sbu/all";
  exportSbusBaseURL: any = "sbu/export";
  getFinancialYearURL: any = "profitCenterFinanceYears/";
  getTerritoriesURL: any = "territories/";
  getCustomersURL: any = "customerByTerritory/";
  getBrandsURL: any = "/brands/dropdown";
  getUserManagementURL: any = "customers/getAllUsers"
  getPrevYearsSalesURL: any = "actualSales/getPreviousYearSales";
  getMaterialMasterURL: any = "material/";
  getBrandVolumeURL: any = "material/total/";
  getAllSbuByPCURL: any = "sbu/byProfitcenter";
  territoriesByZoneCodeURL: any = "territoriesByZoneCode/";
  adduserRole: any = "users/";
  getUserRoleByIdURL: any = "get-user-by-id";
  updateUserRoleByIdURL: any = "customers/update";
  updateuserRole: any = "users/detailsBy";
  changePasswordURL: any = "users/changePassword";
  tmBudgetDetailsURL: any = "tm/budgets/update";
  getPercentageGrowthURL: any = "rewards-points/value";
  getProfitCenterURL: any = "profitCenters/";
  SKUTotalUpdateURL: any = "material/byTotal/";
  getTerritoryURL: any = "territories/profitcenter/zone/";
  getBrandsAllByPagination: any = "brands/getAll/pageable";
  getAllProductURL: any = "products";
  getAllProductExportURL: any = "products";
  getAllSKUsExportURL: any = "sku/export";
  getBrandDetailsByIDURL: any = "brands/byId/";
  getPrevYearSalesDataURL: any = "actualSales/getPreviousYearSales/byCustomer/";
  skuDetailsByBrandIDURL: any = "sku/bybrandId";
  getCustomersURLProfitCenterZoneURL: any = "territories/byTerritoryAndZone";
  custCountURL: any = "territories/count/byZone";
  salesURL: any = "actualSales/byZone";
  addBrandURL: any = "brands/";
  updateBrandURL: any = "brands/updateBrand";
  addSKUURL: any = "sku/addSku";
  updateSKUURL: any = "sku/updateSku";
  distributorStatusURL: any = "distributor";
  retailerStatusURL: any = "retailers";
  farmerStatusURL: any = "farmer";
  adminAfSwalStatusURL: any = "users";
  customerTypeURL: any = "customerType/all";
  skuDetailsByBrandCodeURL: any = "sku/dropdown/byBrand";
  exportUsersDataURL: any = "users/byRole";
  exportDistributorsDataURL: any = "distributor/export";
  exportRetailersDataURL: any = "retailers/all";
  exportFarmersDataURL: any = "farmer/export";
  getAllStateNamesURL: any = "state/all?langId=1";
  getAllRegionNamesURL: any = "";
  eventData: any = [];
  randomNumber: any;
  retailerByTerritory: any = "retailer/byTerritory/dropdown";
  getAllCropsURL: any = 'crops';
  getAllBussinessunit: any = 'scan-categories';
  uploadPackshotURL: any = 'brands/upload-image';
  appUsersBaseURL: any = 'customers/all';
  baseurl: any = environment.baseUrl;
  imageFlag: boolean = true;
  isActive: any;
  roleName: any;
  viewFiles: string = 'files/view'
  downloadFiles: string = 'files/download?'

  constructor(private http: HttpClient, private utility: Utility) {
    const generatedNumber = Math.floor(1000 + Math.random() * 9000);
    this.randomNumber = this.utility.encrypt(generatedNumber.toString());
    const role = localStorage.getItem('role');
    if (role) this.userRoleSubject.next(role);
    this.initUserRole();
  }
  private userRoleSubject = new ReplaySubject<string>(1); // buffer 1

  userRole$ = this.userRoleSubject.asObservable();

  setUserRole(role: string): void {
    let userRole = this.utility.encryptString(role);    
    this.userRoleSubject.next(role);
  }

  initUserRole(): void {
    const role = this.utility.decryptLSDAta (localStorage.getItem('userRole') || '');
    if (role) {
      this.userRoleSubject.next(role);
    }
  }

  // Optional: get current role value without subscribing
  // getUserRole(): string {
  //   return this.userRoleSubject.getValue();
  // }

  public fetchUserRole(): void {
    const userId = localStorage.getItem('userID');
    if (!userId) {
      return;
    }
  
    this.getUserById(userId).subscribe({
      next: (userData: any) => {
        userData = this.utility.decryptString(userData);
        try {
          const role =
            typeof userData === 'string'
              ? JSON.parse(userData)?.role
              : userData?.role;
          if (role) {
            this.setUserRole(role);
          }
        } catch (e) {
          console.error('Error parsing user data:', e);
        }
      },
      error: (err) => {
        console.error('Error fetching user role:', err);
      }
    });
  }
  authorizationKey() {
    let currentToken: any;
    let userToken = this.utility.decryptLSDAta(localStorage.getItem("token"));
    let authorizationToken = userToken;
    // let authorizationToken = localStorage.getItem("token");
    if (authorizationToken) {
      currentToken = authorizationToken;
      console.log("currentToken", currentToken);
    }
    const headers: HttpHeaders = this.createHeader(currentToken);
    return headers;
  }

  private createHeader(authorizationToken: any): HttpHeaders {
    return new HttpHeaders({
      Authorization: `Bearer ${authorizationToken}`,
      'Content-Type': 'application/json'
    });
  }
  
  brandIndexNumber: any;
  getPageIndexBrand() {
    return this.brandIndexNumber;
  }
  setPageIndexBrand(data: any) {
    this.brandIndexNumber = data;
  }

  imageAuthorizationKey() {
    let currentToken: any;
    let userToken = this.utility.decryptLSDAta(localStorage.getItem("token"));
    let authorizationToken = userToken; 
    // let authorizationToken = localStorage.getItem("token");
    if (authorizationToken) {
      currentToken = authorizationToken;
    }
    const headers: HttpHeaders = this.createImageHeader(currentToken);
    return headers;
  }

  private createImageHeader(authorizationToken: any): HttpHeaders {
    return new HttpHeaders({
      Authorization: `Bearer ${authorizationToken}`,
      'imageRequest': `${this.imageFlag}`
    });
  }

  private handleError(error: any) {
    return throwError(() => new Error(error));
  }

  setEventData(data: any) {
    this.eventData = data;
  }
  getEventData(data: any) {
    return (this.eventData = data);
  }

  login(encryptedData: any) {
    encryptedData = this.utility.encrypt(encryptedData);
    const headers = this.authorizationKey();
    return this.http.post(this.baseurl + this.loginURL, encryptedData, {
      headers: headers, responseType: 'text'
    });
  }

  showDocument(fileName: string): Observable<any> {
    const headers = this.authorizationKey();
    let apiUrl = this.baseurl + this.viewFiles;
    const cleanFileName = fileName.startsWith('/') ? fileName.substring(1) : fileName;
    const fullUrl = `${apiUrl}/${cleanFileName}`;
    
    return this.http.get(fullUrl, {
      headers: headers,
      responseType: 'blob'
    }).pipe(
      catchError(error => {
        console.error('Document load error:', error);
        throw new Error(`Failed to load document: ${error.message}`);
      }),
      map(response => {
        return this.detectFileType(response, fileName).pipe(
          map(detectedType => {
            const blob = new Blob([response], { type: detectedType });
            return {
              url: URL.createObjectURL(blob),
              contentType: detectedType
            };
          })
        );
      }),
      switchMap(obs => obs)
    );
  }
  

  private detectFileType(blob: Blob, fileName: string): Observable<string> {
    return new Observable<string>(observer => {
      const isImageByName = fileName.toLowerCase().match(/\.(jpe?g|png|gif|bmp)$/i);
      const fileReader = new FileReader();
      fileReader.onloadend = (e) => {
        const arr = new Uint8Array(e.target?.result as ArrayBuffer).subarray(0, 4);
        let header = '';
        for (let i = 0; i < arr.length; i++) {
          header += arr[i].toString(16);
        }
        let contentType = 'application/pdf';
        
        if (header.startsWith('ffd8ff')) {
          contentType = 'image/jpeg';
        }
        else if (header === '89504e47') {
          contentType = 'image/png';
        }
        else if (header === '47494638') {
          contentType = 'image/gif';
        }
        else if (header.startsWith('25504446')) {
          contentType = 'application/pdf';
        }
        else if (isImageByName) {
          const ext = fileName.toLowerCase().split('.').pop() || '';
          contentType = ext === 'jpg' || ext === 'jpeg' ? 'image/jpeg' : `image/${ext}`;
        }
        observer.next(contentType);
        observer.complete();
      };
      
      fileReader.onerror = () => {
        if (isImageByName) {
          const ext = fileName.toLowerCase().split('.').pop() || '';
          const contentType = ext === 'jpg' || ext === 'jpeg' ? 'image/jpeg' : `image/${ext}`;
          observer.next(contentType);
        } else {
          observer.next('application/pdf');
        }
        observer.complete();
      };
      const slice = blob.slice(0, 4);
      fileReader.readAsArrayBuffer(slice);
    });
  }

  downloadDocument(fileName: any) {
    const headers = this.authorizationKey();
    return this.http.get(this.baseurl + this.viewFiles + fileName, { headers: headers, responseType: 'text' });
  }

  downloadDocumentFiles(fileName: string): Observable<Blob> {
    console.log("fileName",fileName)
    const rawUrl = `${this.baseurl}${this.downloadFiles}fileName=${fileName}`;
    return this.http.get(rawUrl, {
      headers: this.authorizationKey(),
      responseType: 'blob'
    });
  }  
  


  getAllCrops(id: any) {
    const headers = this.authorizationKey();
    return this.http.get(this.baseurl + `crops/dropdown`, { headers: headers, responseType: 'text' });
  }

  getUpdateScheme(id: any) {
    const headers = this.authorizationKey();
    return this.http.get(this.baseurl + `rewards/${id}`, { headers: headers, responseType: 'text' });
  }
  updateScheme(id: any) {
    const headers = this.authorizationKey();
    return this.http.post(this.baseurl + `rewards/${id}`, { headers });
  }
  updateProductToggle(id: any, active: boolean, bonusPercentage: number, startDate?: Date, endDate?: Date) {
    const headers = this.authorizationKey();
    let params: any = { active, bonusPercentage };

    if (startDate) params.startDate = startDate.toISOString().split('T')[0];
    if (endDate) params.endDate = endDate.toISOString().split('T')[0];

    return this.http.post(this.baseurl + "products/" + id, null, {
      headers,
      params,
    });
  }
  updateProductToggleActive(id: any, active: boolean,) {
    const headers = this.authorizationKey();
    let params: any = { active, };

    return this.http.post(this.baseurl + "products/" + id, null, {
      headers,
      params,
    });
  }
  getTerritory() {
    const headers = this.authorizationKey();
    return this.http.get(this.baseurl + `territories/all/dropdown`, {
      headers, responseType: 'text'
    });
  }
  overallScanedChart(body: any) {
    let encryptedData = this.utility.encrypt(body);
    const headers = this.authorizationKey();
    return this.http.post(
      this.baseurl + `dashboard/overallSalesScanned`,
      encryptedData,
      { headers: headers, responseType: 'text' }
    );
  }
  retailerCountChart(body: any) {
    body = this.utility.encrypt(body);
    const headers = this.authorizationKey();
    return this.http.post(this.baseurl + `dashboard/retailerCount`, body, {
      headers: headers, responseType: 'text'
    });
  }
  getAllRegion(zoneCode: any) {
    const headers = this.authorizationKey();
    return this.http.get(
      this.baseurl + `region/dropdown/byZone?zoneIds=${zoneCode}`,
      { headers: headers, responseType: 'text' }
    );
  }
  getAllRegions() {
    const headers = this.authorizationKey();
    return this.http.get(this.baseurl + `region/dropdown/byZone?zoneIds=`, {
      headers, responseType: 'text'
    });
  }

  getAllAdminRegion() {
    const headers = this.authorizationKey();
    return this.http.get(this.baseurl + "regions/dropdown/byZone", {
      headers, responseType: 'text'
    });
  }

  topRetailerByDistrict() {
    const headers = this.authorizationKey();
    return this.http.get(this.baseurl + `districts/dropdown`, {
      headers: headers, responseType: 'text'
    });
  }

  topRetailerByDistrictForTerritoryManager(territoryCode: any) {
    const headers = this.authorizationKey();
    return this.http.get(
      this.baseurl + "districts/dropdown",
      { headers: headers, responseType: 'text' }
    );
  }
  topRetailerByDistrictForRegionalManager(regionCode: any) {
    const headers = this.authorizationKey();
    return this.http.get(
      this.baseurl + `district/dropdown?regionCode=${regionCode}`,
      { headers: headers, responseType: 'text' }
    );
  }

  uploadProfileImage(data: any): Observable<any> {
    const headers = this.imageAuthorizationKey();
    return this.http
      .post(this.baseurl + this.imageApiUrl, data, { headers: headers, responseType: 'text' })
      .pipe(catchError(this.handleError));
  }



  getUserDetailsById(userId: any) {
    let URL = `${this.getUserRoleByIdURL}/${userId}`;

    const headers = this.authorizationKey();
    return this.http.get(this.baseurl + URL, { headers: headers, responseType: 'text' });
  }

  getDropdownCropData() {
    let URL = this.getAllCropsURL;
    const headers = this.authorizationKey();
    return this.http.get(this.baseurl + URL, {
      headers: headers,
      responseType: 'text',
    });
  }
  getUserManagementData(data: {
    isAdmin: boolean;
    role: string;
    profitCenterCode: string;
    pageLimit: number;
    currentPage: number;
    searchedValue: string;
    isActive: boolean;
    unPaged: boolean;
    agreementValue: string
  }) {
  
    const role = data.role; // Directly use the role as it's already mapped  
    const URL = `${this.getUserManagementURL}?` +
      `&role=${role}` +
      `&unPaged=false` +
      `&agreementStatus=${data.agreementValue}` +
      `&page=${data.currentPage}` +
      `&size=${data.pageLimit}` +
      `&searchedValue=${data.searchedValue}` +
      `&isActive=${data.isActive}` +
      `&unPaged=${data.unPaged}`;
  
    const headers = this.authorizationKey();
    return this.http.get(this.baseurl + URL, {
      headers: headers,
      responseType: 'text',
    });
  }
  



  getBussinessUnit() {
    let URL = this.getAllBussinessunit;
    const headers = this.authorizationKey();
    return this.http.get(this.baseurl + URL, {
      headers: headers,
      responseType: 'text',
    });
  }

  uploadPackshot(formData: any) {
    let URL = this.uploadPackshotURL;
    const headers = this.authorizationKey();
    return this.http.post(this.baseurl + URL, formData, {
      headers: headers,
      responseType: 'text',
    });
  }

  getAllProduct(data: any) {
    let URL = this.getAllProductURL +
      "?sortBy=productName" +
      "&sortDir=asc" +
      "&page=" + data.currentPage +
      "&size=" + data.pageLimit +
      "&unPaged=false" +
      "&searchedValue=" + (data.searchedValue || '');
  
    // Add active parameter if specified (for Active or Inactive tabs)
    if (data.isActive !== undefined) {
      URL += "&active=" + data.isActive;
    }

    const headers = this.authorizationKey();

    return this.http.get(this.baseurl + URL, {
      headers: headers,
      responseType: 'text'
    });
  }

  getAllProductExport(data: any) {
    let URL =
      this.getAllProductExportURL +
      "?sortBy=productName" +
      "&sortDir=asc" +
      "&unPaged=true" +
      "&searchedValue=" +
      (data.searchedValue || '');
    
    // Add active parameter if specified (for Active or Inactive tabs)
    if ((data as any).isActive !== undefined) {
      URL += "&active=" + (data as any).isActive;
    }
    
    const headers = this.authorizationKey();
    return this.http.get(this.baseurl + URL, { headers: headers, responseType: 'text' });
  }

  getSKUsExport(id: any) {
    let URL = this.getAllSKUsExportURL + "?brandId=" + id;
    const headers = this.authorizationKey();
    return this.http.get(this.baseurl + URL, { headers: headers, responseType: 'text' });
  }


  getAllUsers(data: any) {
    let URL =
      this.usersBaseURL +
      "?role=" +
      data.role +
      "&profitCenterCode=" +
      data.profitCenterCode +
      "&page=" +
      data.currentPage +
      "&size=" +
      data.pageLimit +
      "&searchedValue=" +
      data.searchedValue +
      "&isActive=" +
      data.isActive;
    const headers = this.authorizationKey();
    return this.http.get(this.baseurl + URL, { headers: headers, responseType: 'text' });
  }

  getAllAppUsers(data: any) {
    const headers = this.authorizationKey();
    return this.http.get(
      this.baseurl +
      this.appUsersBaseURL +
      '?page=' +
      data.currentPage +
      '&size=' +
      data.pageLimit +
      '&searchedValue=' +
      data.searchedValue +
      '&unPaged=' +
      data.unPaged +
      '&customerTypeId=' +
      data.customerTypeId +
      '&districts=' +
      data.districts +
      '&isActive=' +
      data.isActive +
      '&sort=createdDate,desc',
      {
        headers: headers,
        responseType: 'text',
      }
    );
  }

  getAllRetailers(data: any) {
    let URL =
      this.retailersBaseURL +
      "?page=" +
      data.currentPage +
      "&size=" +
      data.pageLimit +
      "&searchedValue=" +
      data.searchedValue +
      "&isActive=" +
      data.isActive;
    const headers = this.authorizationKey();
    return this.http.get(this.baseurl + URL, { headers: headers, responseType: 'text' });
  }

  setUserActive(user: any) {
    this.isActive = user;
  }

  getAllCustomerRole() {
    let URL = 'customers/roles';
    const headers = this.authorizationKey();
    return this.http.get(this.baseurl + URL, {
      headers: headers,
      responseType: 'text',
    });
  }

  getUserActive() {
    return this.isActive;
  }

  getRoleName() {
    return this.roleName;
  }


  getAdminEmoployeeDetailsById(userId: any) {
    let URL = this.getUserRoleByIdURL;
    const headers = this.authorizationKey();
    return this.http.get(this.baseurl + URL + '/' + userId, {
      headers: headers,
      responseType: 'text',
    });
  }

  getAppUserDetailsById(userId: any) {
    const headers = this.authorizationKey();
    return this.http.get(this.baseurl + 'get-user-by-id/' + userId, {
      responseType: 'text',
      headers: headers,
    });
  }


  updateAppUsersStatus(body: any) {
    const headers = this.authorizationKey();
    
    // Ensure isActive is a boolean value
    const isActive = body.active === true;
    
    return this.http.post(
      `${this.baseurl}customers/toggle-status?isActive=${isActive}&id=${body.id}`, 
      null, 
      {
        headers: headers,
        responseType: 'text',
      }
    );
  }

  updateRetailerStatus(body: any) {
    const headers = this.authorizationKey();
    return this.http.post(
      this.baseurl + this.retailerStatusURL + '/' + body.id,
      body,
      {
        headers: headers,
        responseType: 'text',
      }
    );
  }

  getAllCustomersType() {
    const headers = this.authorizationKey();
    return this.http.get(this.baseurl + this.customerTypeURL, {
      headers: headers,
      responseType: 'text',
    });
  }

  getAppUsersExportData(data: any) {
    const headers = this.authorizationKey();
    return this.http.get(
      this.baseurl +
      this.appUsersBaseURL +
      '?searchedValue=' +
      data.searchedValue +
      '&unPaged=' +
      data.unPaged +
      '&customerTypeId=' +
      data.customerTypeId +
      '&customerTypeId=' +
      data.customerTypeId +
      '&districts=' +
      data.districts +
      '&isActive=' +
      data.isActive +
      '&sort=createdDate,desc',
      {
        headers: headers,
        responseType: 'text',
      }
    );
  }

  getAllUsersExportData(data: any) {
    const headers = this.authorizationKey();
    const URL = `${this.getUserManagementURL}?` +
      `&isAdmin=${data.isAdmin}` + 
      `&role=${data.role}` +
      `&unPaged=false` +
      `&searchedValue=${data.searchedValue}` +
      `&isActive=${data.isActive}` +
      `&unPaged=${data.unPaged}`;
 
    return this.http.get(this.baseurl + URL, {
      headers: headers,
      responseType: 'text',
    });
  }


  getAllFarmers(data: any) {
    let URL =
      this.farmersBaseURL +
      "?page=" +
      data.currentPage +
      "&size=" +
      data.pageLimit +
      "&searchedValue=" +
      data.searchedValue +
      "&isActive=" +
      data.isActive;
    const headers = this.authorizationKey();
    return this.http.get(this.baseurl + URL, { headers: headers, responseType: 'text' });
  }

  getAllSbus(data: any) {
    let URL =
      this.sbusBaseURL +
      "?page=" +
      data.currentPage +
      "&size=" +
      data.pageLimit +
      "&searchedValue=" +
      data.searchedValue;
    const headers = this.authorizationKey();
    return this.http.get(this.baseurl + URL, { headers: headers, responseType: 'text' });
  }

  getAllSbusExportData() {
    const headers = this.authorizationKey();
    return this.http.get(this.baseurl + this.exportSbusBaseURL, {
      headers: headers, responseType: 'text'
    });
  }

  getFinancialYear(data: any) {
    let URL = this.getFinancialYearURL + data.params + "?userId=" + data.id;
    const headers = this.authorizationKey();
    return this.http.get(this.baseurl + URL, { headers: headers, responseType: 'text' });
  }

  getAllTerritory(data: any) {
    let URL = this.getTerritoriesURL + data.params + "/" + data.id;
    const headers = this.authorizationKey();
    return this.http.get(this.baseurl + URL, { headers: headers, responseType: 'text' });
  }

  getAllCustomers(data: any) {
    let URL = this.getCustomersURL + data.code;
    const headers = this.authorizationKey();
    return this.http.get(this.baseurl + URL, { headers: headers, responseType: 'text' });
  }

  getBrands() {
    let URL = this.getBrandsURL;
    const headers = this.authorizationKey();
    return this.http.get(this.baseurl + URL, { headers: headers, responseType: 'text' });
  }

  getAllPreviousYearSales(data: any) {
    let URL =
      this.getPrevYearsSalesURL +
      "?customerCode=" +
      data.customerCode +
      "&territoryCode=" +
      data.territoryCode +
      "&profitCenterCode=" +
      data.profitCenterCode +
      "&brandCode=" +
      data.brandCode +
      "&profitCenterFinanceYearsId=" +
      data.profitCenterFinanceYearsId;
    const headers = this.authorizationKey();
    return this.http.get(this.baseurl + URL, { headers: headers, responseType: 'text' });
  }

  materialMaster(data: any) {
    let URL =
      this.getMaterialMasterURL +
      "?customerCode=" +
      data.customerCode +
      "&brandCode=" +
      data.brandCode +
      "&territoryCode=" +
      data.territoryCode +
      "&profitCenterCode=" +
      data.profitCenterCode +
      "&profitCenterFinanceYearId=" +
      data.profitCenterFinanceYearsId;
    const headers = this.authorizationKey();
    return this.http.get(this.baseurl + URL, { headers: headers, responseType: 'text' });
  }

  brandVolumeDetails(data: any) {
    let URL =
      this.getBrandVolumeURL +
      "?customerCode=" +
      data.customerCode +
      "&brandCode=" +
      data.brandCode +
      "&territoryCode=" +
      data.territoryCode +
      "&profitCenterCode=" +
      data.profitCenterCode +
      "&profitCenterFinanceYearId=" +
      data.profitCenterFinanceYearsId;
    const headers = this.authorizationKey();
    return this.http.get(this.baseurl + URL, { headers: headers, responseType: 'text' });
  }

  getAllSbuByPC(data: any) {
    let URL = this.getAllSbuByPCURL + "?profitCenterCode=" + data;
    const headers = this.authorizationKey();
    return this.http.get(this.baseurl + URL, { headers: headers, responseType: 'text' });
  }

  addUser(body: any) {
    const headers = this.authorizationKey();
    return this.http.post(this.baseurl + "get-user-by-id", body, { headers: headers, responseType: 'text' });
  }

  updateUser(body: any) {
    body = this.utility.encryptString(body)
    body = (body);
    let URL = this.updateUserRoleByIdURL;
    const headers = this.authorizationKey();
    return this.http.post(this.baseurl + URL, body, {
      headers: headers, responseType: 'text'
    });
  }

  changePassword(body: any, id: any) {
    body = this.utility.encrypt(body);
    const headers = this.authorizationKey();
    return this.http.post(
      this.baseurl + "users/changePassword/" + id,
      body,
      { headers: headers, responseType: 'text' }
    );
  }

  updateSKU(data: any) {
    let URL =
      this.getMaterialMasterURL +
      "byCustomerCode?customerCode=" +
      data.customerCode +
      "&brandCode=" +
      data.brandCode +
      "&territoryCode=" +
      data.territoryCode +
      "&profitCenterCode=" +
      data.profitCenterCode +
      "&profitCenterFinanceYearId=" +
      data.profitCenterFinanceYearsId;
    const headers = this.authorizationKey();

    return this.http.post(this.baseurl + URL, { headers: headers, responseType: 'text' });
  }

  getProfitCenter(data: any) {
    let URL = this.getProfitCenterURL + data.params + "/" + data.id;
    const headers = this.authorizationKey();
    return this.http.get(this.baseurl + URL, { headers: headers, responseType: 'text' });
  }

  SKUTotalUpdate(data: any) {
    let URL =
      this.SKUTotalUpdateURL +
      "?customerCode=" +
      data.customerCode +
      "&brandCode=" +
      data.brandCode +
      "&territoryCode=" +
      data.territoryCode +
      "&profitCenterCode=" +
      data.profitCenterCode +
      "&profitCenterFinanceYearId=" +
      data.profitCenterFinanceYearsId;
    const headers = this.authorizationKey();
    return this.http.post(this.baseurl + URL, { headers: headers, responseType: 'text' });
  }

  getTerritoryZonalManager(data: any) {
    let URL = this.getTerritoryURL + data.profitCenter + "/" + data.zoneCode;
    const headers = this.authorizationKey();
    return this.http.get(this.baseurl + URL, { headers: headers, responseType: 'text' });
  }

  getDistributorDetailsByID(userId: any) {
    let URL = this.getUserRoleByIdURL + userId;
    const headers = this.authorizationKey();
    return this.http.get(this.baseurl + URL, { headers: headers, responseType: 'text' });
  }

  getRetailerDetailsByID(userId: any) {
    let URL = this.getUserRoleByIdURL + userId;
    const headers = this.authorizationKey();
    return this.http.get(this.baseurl + URL, { headers: headers, responseType: 'text' });
  }

  getFarmerDetailsByID(userId: any) {
    let URL = this.getUserRoleByIdURL + userId;
    const headers = this.authorizationKey();
    return this.http.get(this.baseurl + URL, { headers: headers, responseType: 'text' });
  }

  getBrandDetailsByID(data: any) {
    let URL = this.getBrandDetailsByIDURL + data.id;
    const headers = this.authorizationKey();
    return this.http.get(this.baseurl + URL, { headers: headers, responseType: 'text' });
  }

  updateBrand(data: any) {
    data = this.utility.encrypt(data);
    const headers = this.authorizationKey();
    return this.http.post(
      this.baseurl + this.updateBrandURL,
      data,
      {
        headers: headers, responseType: 'text'
      }
    );
  }

  addBrand(body: any) {
    body = this.utility.encrypt(body);
    const headers = this.authorizationKey();
    return this.http.post(this.baseurl + this.addBrandURL, body, {
      headers: headers, responseType: 'text'
    });
  }

  skuDetails(data: any) {
    let URL = this.skuDetailsByBrandIDURL + "?brandId=" + data;
    const headers = this.authorizationKey();
    return this.http.get(this.baseurl + URL, { headers: headers, responseType: 'text' });
  }

  skuDetailsByBrandCode(data: any) {
    let URL = this.skuDetailsByBrandCodeURL + "?brandCodes=" + data;
    const headers = this.authorizationKey();
    return this.http.get(this.baseurl + URL, { headers: headers, responseType: 'text' });
  }

  addSKUDetails(body: any) {
    const headers = this.authorizationKey();
    return this.http.post(this.baseurl + this.addSKUURL, body, {
      headers: headers, responseType: 'text'
    });
  }

  updateSKUDetails(data: any) {
    const headers = this.authorizationKey();
    return this.http.post(
      this.baseurl + this.updateSKUURL + "/" + data.id,
      data,
      {
        headers: headers, responseType: 'text'
      }
    );
  }

  getAllCustomersByPCCodeandZone(terrCode: any, zone: any, profitCenter: any) {
    let URL =
      this.getCustomersURLProfitCenterZoneURL +
      "?territoryCode=" +
      terrCode +
      "&zoneCode=" +
      zone +
      "&profitCenterCode=" +
      profitCenter;
    const headers = this.authorizationKey();
    return this.http.get(this.baseurl + URL, { headers: headers, responseType: 'text' });
  }

  customerCount(data: any) {
    let URL =
      this.custCountURL +
      "?zoneCode=" +
      data.zone +
      "&profitCenterCode=" +
      data.profitCenter;
    const headers = this.authorizationKey();
    return this.http.get(this.baseurl + URL, { headers: headers, responseType: 'text' });
  }

  getSales(data: any) {
    let URL =
      this.salesURL +
      "?customerCode=" +
      data.customerCode +
      "&brandCode=" +
      data.brandCode +
      "&territoryCode=" +
      data.territroyCode +
      "&zoneCode=" +
      data.zone +
      "&profitCenterCode=" +
      data.profitCenter +
      "&profitCenterfinanceYearId=" +
      data.profitFinanceYearsId +
      "&view=" +
      data.view +
      "&size=" +
      data.pageLimit +
      "&page=" +
      data.currentPage;
    const headers = this.authorizationKey();
    return this.http.get(this.baseurl + URL, { headers: headers });
  }

  updateUserStatus(body: any) {
    let decryptedString: string;
    let parsedBody: any;
    try {
      decryptedString = this.utility.decryptString(body);
      parsedBody = JSON.parse(decryptedString);
    } catch (err) {
      console.error('Failed to decrypt or parse body:', err);
      throw err;
    }
  
    const headers = this.authorizationKey();
    const isActive = parsedBody.active === true;
    const id = parsedBody.id;
  
    return this.http.post(
      `${this.baseurl}customers/toggle-status?isActive=${isActive}&id=${id}`,
      null,
      {
        headers: headers,
        responseType: 'text',
      }
    );
  }
  

  
  

  addAppUsers(requestPayload: any) {
    const headers = this.authorizationKey();
    return this.http.post(this.baseurl + 'customers', requestPayload, {
      headers: headers,
      responseType: 'text',
    });
  }

  updateAppUsers(requestPayload: any) {
   const payload = this.utility.encryptString(requestPayload);
    const headers = this.authorizationKey();
    return this.http.post(this.baseurl + 'customers/update', payload, {
      headers: headers,
      responseType: 'text',
    });
  }

  downloadTemplateBulkUpload() {
    const headers = this.authorizationKey();
    return this.http.get(this.baseurl + 'customers/download-template', {
      responseType: 'blob',
      headers: headers,
    });
  }

  updateDistributorStatus() {
    URL = this.distributorStatusURL;
    const headers = this.authorizationKey();
    return this.http.get(this.baseurl + URL, { headers: headers, responseType: 'text' });
  }


  updateFarmerStatus() {
    URL = this.farmerStatusURL;
    const headers = this.authorizationKey();
    return this.http.get(this.baseurl + URL, { headers: headers });
  }


  getAllFarmersExportData(data: any) {
    let URL = this.exportFarmersDataURL + "?isActive=" + data.isActive;
    const headers = this.authorizationKey();
    return this.http.get(this.baseurl + URL, { headers: headers });
  }

  getAllRetailersExportData(data: any) {
    let URL = this.exportRetailersDataURL + "?isActive=" + data.isActive + "&size=" +
      data.pageLimit +
      "&page=" +
      data.currentPage + "&searchedValue=" +
      data.searchedValue;
    const headers = this.authorizationKey();
    return this.http.get(this.baseurl + URL, { headers: headers, responseType: 'text' });
  }

  getAllDistributorsExportData(data: any) {
    let URL =
      this.exportDistributorsDataURL +
      "?profitCenterCode=" +
      data.profitCenterCode +
      "&isActive=" +
      data.isActive +
      "&isSignedUp=" +
      data.signedUp;
    const headers = this.authorizationKey();
    return this.http.get(this.baseurl + URL, { headers: headers, responseType: 'text' });
  }

  setRoleName(role: any) {
    this.roleName = role;
  }

  bulkUploadFileBulkUpload(requestPayload: any) {
    const headers = this.authorizationKey();
    return this.http.post(
      this.baseurl + 'customers/import',
      requestPayload.file,
      { headers: headers, responseType: 'text' }
    );
  }

  getAllStateNames() {
    const headers = this.authorizationKey();
    return this.http.get(this.baseurl + this.getAllStateNamesURL, {
      headers: headers, responseType: 'text'
    });
  }

  getAllRegionNames() {
    const headers = this.authorizationKey();
    return this.http.get(this.baseurl + this.getAllRegionNamesURL, {
      headers: headers, responseType: 'text'
    });
  }

  getRegion() {
    // 'http://localhost:8090/api/states/all' \
    const headers = this.authorizationKey();
    const URL =
      this.baseurl +
      'region/get-all'
    return this.http.get(URL, {
      headers: headers,
      responseType: 'text',
    });
  }

  getStateByID(data: any) {
    const headers = this.authorizationKey();
    const URL =
      this.baseurl +
      'state/get-all/' + data.stateId
    return this.http.get(URL, {
      headers: headers,
      responseType: 'text',
    });
  }

  getZoneById(data: any) {
    const headers = this.authorizationKey();
    const URL =
      this.baseurl +
      'zone/get-all/' + data.zoneId
    return this.http.get(URL, {
      headers: headers,
      responseType: 'text',
    });
  }

  getCityById(data: any) { 
    const headers = this.authorizationKey();
    const URL =
      this.baseurl +
      'city/get-all/' + data.cityId
    return this.http.get(URL, {
      headers: headers,
      responseType: 'text',
    });
  }

  getCropById(data: any) {
    const headers = this.authorizationKey();
    const URL =
      this.baseurl +
      'crop/get-all/' + data.zoneId
    return this.http.get(URL, {
      headers: headers,
      responseType: 'text',
    });
  }

  addUserData(requestPayload: any) {
    // const headers = this.authorizationKey();
    return this.http.post(this.baseurl + 'customer-auth/signup', requestPayload, {
 
      responseType: 'text',
    });
  }

  // Generate OTP for email (with auth - for authenticated users)
  generateEmailOTP(payload: any) {
    payload = this.utility.encryptString(payload);
    const headers = this.authorizationKey();
    return this.http.post(this.baseurl + 'otps/email/generate', payload, {
      headers: headers,
      responseType: 'text'
    });
  }

  // Generate OTP for mobile (with auth - for authenticated users)
  generateMobileOTP(payload: any) {
    payload = this.utility.encryptString(payload);
    const headers = this.authorizationKey();
    return this.http.post(this.baseurl + 'otps/generate', payload, {
      headers: headers,
      responseType: 'text'
    });
  }

  // Validate OTP (with auth - for authenticated users)
  validateOTP(payload: any) {
    const encryptedPayload = this.utility.encrypt(JSON.stringify(payload));
    const headers = this.authorizationKey();
    return this.http.post(this.baseurl + 'otps/validate', { encryptedBody: encryptedPayload }, {
      headers: headers,
      responseType: 'text'
    });
  }

  // Generate OTP for email and mobile (without auth - for login)
  generateOTPForLogin(payload: any) {
    const encryptedPayload = this.utility.encrypt(JSON.stringify(payload));
    const headers = new HttpHeaders({
      'Content-Type': 'application/json'
    });
    return this.http.post(this.baseurl + 'otps/generate', { encryptedBody: encryptedPayload }, {
      headers: headers,
      responseType: 'text'
    });
  }

  // Generate OTP for email (without auth - for login)
  generateEmailOTPForLogin(payload: any) {
    const encryptedPayload = this.utility.encrypt(JSON.stringify(payload));
    const headers = new HttpHeaders({
      'Content-Type': 'application/json'
    });
    return this.http.post(this.baseurl + 'otps/email/generate', { encryptedBody: encryptedPayload }, {
      headers: headers,
      responseType: 'text'
    });
  }

  // Generate OTP for mobile (without auth - for login)
  generateMobileOTPForLogin(payload: any) {
    const encryptedPayload = this.utility.encrypt(JSON.stringify(payload));
    const headers = new HttpHeaders({
      'Content-Type': 'application/json'
    });
    return this.http.post(this.baseurl + 'otps/generate', { encryptedBody: encryptedPayload }, {
      headers: headers,
      responseType: 'text'
    });
  }

  // Validate OTP (without auth - for login)
  validateOTPForLogin(payload: any) {
    const encryptedPayload = this.utility.encrypt(JSON.stringify(payload));
    const headers = new HttpHeaders({
      'Content-Type': 'application/json'
    });
    return this.http.post(this.baseurl + 'otps/validate', { encryptedBody: encryptedPayload }, {
      headers: headers,
      responseType: 'text'
    });
  }
  getUserById(id:any){
    // 'http://localhost:8090customers/11'
       const headers = this.authorizationKey();
       const URL =
         this.baseurl +
         'get-user-by-id/' + id
       return this.http.get(URL, {
         headers: headers,
         responseType: 'text',
       });
  }

  getAllAgreement(id:any){
    const headers = this.authorizationKey();
    const URL =
      this.baseurl +
      'agreement/get-all?userId=' + id+
      '&page='+ 0 +'&size=' + 100
    return this.http.get(URL, {
      headers: headers,
      responseType: 'text',
    });
}

uploadAgreement(body: FormData, data: any) {
 let headers = this.authorizationKey();
 if (headers.has('Content-Type')) {
   headers = headers.delete('Content-Type');
 }
 
 return this.http.post(
   `${this.baseurl}agreement/upload-agreement?userId=${data.userId}&startDate=${data.startDate}&endDate=${data.endDate}`,
   body,
   { headers }
 );
}


deleteAgreementById(data: any) {
  const headers = this.authorizationKey();
  const URL = `${this.baseurl}agreement/delete?agreementId=${data}`;
  return this.http.get(URL, {
    headers: headers,
    responseType: 'text',
  });
}

getAgreementStatus() {
  const headers = this.authorizationKey();
  const URL =
    this.baseurl +
    'agreement/get-all-status'
  return this.http.get(URL, {
    headers: headers,
    responseType: 'text',
  });
}
}

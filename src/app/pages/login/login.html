<div *ngIf="!notSignInScreen" class="login-container">
  <div class="language-change-button-container">
    <button mat-button [matMenuTriggerFor]="languageMenu" class="language-change-button" (click)="toggleMenu()">
      <img [src]="'../../assets/img/language.svg'" alt="">
      <span class="text-label">{{ changedLanguage }}</span>
        <img [src]="isMenuOpen ?  '../../assets/img/expand_open.svg': '../../assets/img/expand_more_black_24dp.svg' " alt="">
    </button>
  </div>
  <div class="welcome-element-container">
    <div class="center-container">
      <p id="welcome-para">{{ 'WELCOME_TEXT' | translate }}</p>
      <h1 id="heading-one">{{ 'uplLiderProgram' | translate }}</h1>
    </div>
  </div>
  <div class="element-container">
    <form>
      <div class="logo-container">
        <img src="../../../assets/img/upl.svg" alt="" class="upl-logo">
      </div>

      <!-- User Type Toggle -->
      <div class="user-type-toggle">
        <button
          type="button"
          class="toggle-btn"
          [class.active]="selectedUserType === 'leader'"
          (click)="selectUserType('leader')">
          Leader
        </button>
        <button
          type="button"
          class="toggle-btn"
          [class.active]="selectedUserType === 'employee'"
          (click)="selectUserType('employee')">
          UPL Employee
        </button>
      </div>

      <!-- Content based on selected user type -->
      <div class="login-content">
        <!-- Leader Tab Content -->
        <div *ngIf="selectedUserType === 'leader'" class="leader-content">
          <div class="input-group">
            <label for="mobileOrEmail">Mobile Number or Email</label>
            <input
              [(ngModel)]="leaderForm.value.mobileOrEmail"
              (input)="onMobileOrEmailInput($event)"
              type="text"
              id="mobileOrEmail"
              placeholder="Enter Mobile Number or Email"
              class="form-input"
              [class.error]="leaderForm.get('mobileOrEmail')?.invalid && leaderForm.get('mobileOrEmail')?.touched"
              [disabled]="otpSent"
            />
            <!-- Error message -->
            <div
              *ngIf="leaderForm.get('mobileOrEmail')?.invalid && leaderForm.get('mobileOrEmail')?.touched"
              class="error-message">
              {{ getMobileOrEmailError() }}
            </div>
          </div>

          <!-- OTP Input Field (shown after OTP is sent) -->
          <div *ngIf="showOTPField" class="input-group">
            <label for="otp">Enter OTP</label>
            <input
              [(ngModel)]="leaderForm.value.otp"
              (input)="leaderForm.patchValue({otp: $any($event.target).value})"
              type="text"
              id="otp"
              placeholder="Enter OTP"
              class="form-input"
              [class.error]="leaderForm.get('otp')?.invalid && leaderForm.get('otp')?.touched"
              maxlength="6"
            />
            <!-- OTP Error message -->
            <div
              *ngIf="leaderForm.get('otp')?.invalid && leaderForm.get('otp')?.touched"
              class="error-message">
              Please enter a valid OTP (4-6 digits)
            </div>
          </div>

          <button
            class="otp-button"
            [disabled]="isGetOTPDisabled()"
            (click)="onGetOTP()">
            {{ getButtonText() }}
          </button>

          <p class="signup-text">Don't have an account?</p>
          <button type="button" class="signup-button" (click)="onSignUp()">Sign Up</button>
        </div>

        <!-- UPL Employee Tab Content (existing design) -->
        <div *ngIf="selectedUserType === 'employee'" class="employee-content">
          <p id="para">{{ 'LOGIN_TO_CONTINUE' | translate}}</p>
          <button class="sign-in-button" (click)="onSignIn()">{{ 'LOG_IN' | translate}}</button>
        </div>
      </div>
    </form>
  </div>
</div>
<div class="login-page" *ngIf="notSignInScreen == true">
  <video 
    class="centered-video" 
    autoplay 
    muted 
    loop 
    playsinline
  >
    <source src="../../../assets//video/upl-splash-video.mp4" type="video/mp4">
    Your browser does not support the video tag.
  </video>
  <p class="text">{{ "Please wait for a while" }}</p>
</div>

<!-- Leader Signup Popup -->
<app-leader-signup
  *ngIf="showSignupPopup"
  (close)="onCloseSignup()">
</app-leader-signup>


<mat-menu
  [xPosition]="'before'"
  [yPosition]="'below'"
  [overlapTrigger]="false"
  class="custom-menu-right"
  #languageMenu="matMenu"
  (closed)="menuClosed()"
>
<div class="translation-popup">
  <mat-radio-group
    aria-labelledby="language-radio-group"
    class="language-radio-group"
    [(ngModel)]="selectedLanguage"
    (change)="onLanguageChange($event)">
    
    <mat-radio-button class="language-radio-button" value="en">
      <span>{{ 'english' | translate }}</span>
    </mat-radio-button><br />
    
    <mat-radio-button class="language-radio-button" value="es">
      <span>{{ 'spanish' | translate }}</span>
    </mat-radio-button>
  </mat-radio-group>
</div>
</mat-menu>





<!-- <div class="login-container">
  <div class="row login-segment">
    <div class="login-body">
      <div style="padding: 5%">
        <div class="col-md-12 sign-in">
          <div class="sign-in-text">Sign In</div>
        </div>
        <form
          [formGroup]="form"
          (ngSubmit)="onSubmit(form.value)"
          class="form-horizontal"
          autocomplete="off"
        >
          <div
            class="form-group row col-md-12 input-button email-div"
            [ngClass]="{'has-error': (!form.get('email')?.valid && form.get('email')?.dirty), 'has-success': (form.get('email')?.valid && form.get('email')?.dirty)}"
          >
            <div class="input-group">
              <span class="input-group-addon" id="basic-addon1">
                <i class="fa fa-envelope" aria-hidden="true"></i>
              </span>
              <input
                formControlName="email"
                type="email"
                class="form-control right-border"
                id="inputEmail3"
                placeholder="Email"
                autofocus
                tabindex="1"
                autocomplete="off" 
              />
            </div>
            <div class="sign-up-error">
              <span
                *ngIf="!form.get('email')?.valid && form.get('email')?.dirty"
                class="help-block sub-little-error confpass"
                >Email is {{!form.get('email')?.valid &&
                form.get('email')?.value === ''?'required':'not valid'}}</span
              >
            </div>
          </div>

          <div
            class="form-group row col-md-12 input-button passwordtext"
            [ngClass]="{'has-error': (!form.get('password')?.valid && form.get('password')?.dirty), 'has-success': (form.get('password')?.valid && form.get('password')?.dirty)}"
          >
            <div class="input-group">
              <span class="input-group-addon" id="basic-addon"
                ><i class="fa fa-key" aria-hidden="true"></i
              ></span>
              <input
                formControlName="password"
                [type]="hide ? 'password' : 'text'"
                class="form-control right-border"
                id="inputPassword3"
                placeholder="Password"
                maxlength="16"
                minlength="6"
                tabindex="2"
                autocomplete="off" 
              />
              <i
                (click)="myFunction()"
                class="hide-eye far"
                [ngClass]="{'fa-eye-slash': hide, 'fa-eye': !hide}"
              ></i>
            </div>
            <div class="sign-up-error"> 
              <span *ngIf="!form.get('password')?.hasError('required') && form.get('password')?.hasError('minlength') && form.get('password')?.dirty; else invalidPasswordError">
                <span class="help-block sub-little-error confpass">Requires at least 6 characters long</span>
              </span>
              <ng-template #invalidPasswordError>
                <span *ngIf="!form.get('password')?.valid && form.get('password')?.dirty" class="help-block sub-little-error confpass">Invalid password.</span>
              </ng-template>
            </div>
          </div>
          <div class="row">
            <section class="example-section margin-check">
              <mat-checkbox
                class="example-margin"
                [checked]="true"
                [color]="checkColor"
              >Remember Me</mat-checkbox>
            </section>
          </div>
          

          <div class="form-group row col-md-12 input-button submit-button">
            <input
              [ngClass]="{'disable-submit' : (!form.valid || submitted) }"
              [disabled]="!form.valid || submitted"
              type="submit"
              class="btn-style"
              tabindex="3"
              value="SIGN IN"
            />
          </div>
        </form>
      </div>
    </div>
  </div>
</div> -->



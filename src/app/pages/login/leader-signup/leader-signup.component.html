<div class="signup-overlay" (click)="onOverlayClick($event)">
  <div class="signup-modal" (click)="$event.stopPropagation()">
    <!-- Header -->
    <div class="signup-header">
      <button class="back-btn" (click)="onBack()">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
          <path d="M19 12H5M12 19L5 12L12 5" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
      </button>
      <h2>Leader SignUp</h2>
    </div>

    <!-- Progress Steps -->
    <div class="progress-steps">
      <div class="step" [class.active]="currentStep >= 1" [class.completed]="currentStep > 1">
        <div class="step-icon">
          <img [src]="currentStep >= 1 ? 'assets/img/fill-contacts.svg' : 'assets/img/unfill-contact.svg'" alt="Contact Step" width="35" height="35">
        </div>
      </div>
      <div class="step-line" [class.completed]="currentStep > 1"></div>
      <div class="step" [class.active]="currentStep >= 2" [class.completed]="currentStep > 2">
        <div class="step-icon">
          <img [src]="currentStep >= 2 ? 'assets/img/fill-addresses.svg' : 'assets/img/unfill-address.svg'" alt="Address Step" width="35" height="35">
        </div>
      </div>
      <div class="step-line" [class.completed]="currentStep > 2"></div>
      <div class="step" [class.active]="currentStep >= 3">
        <div class="step-icon">
          <img [src]="currentStep >= 3 ? 'assets/img/fill-documents.svg' : 'assets/img/unfill-document.svg'" alt="Document Step" width="35" height="35">
        </div>
      </div>
    </div>

    <!-- Step Content -->
    <div class="step-content">
      <!-- Step 1: Personal Information -->
      <div *ngIf="currentStep === 1" class="step-form">
        <form [formGroup]="step1Form">
          <div class="form-row">
            <div class="form-group">
              <label>First Name<span class="required">*</span></label>
              <input type="text" formControlName="firstName" placeholder="Enter First Name" class="form-input" maxlength="50">
              <div *ngIf="step1Form.get('firstName')?.invalid && step1Form.get('firstName')?.touched" class="error-message">
                <span *ngIf="step1Form.get('firstName')?.errors?.['required']">First name is required</span>
                <span *ngIf="step1Form.get('firstName')?.errors?.['pattern']">Only letters and spaces are allowed</span>
                <span *ngIf="step1Form.get('firstName')?.errors?.['minlength']">Minimum 2 characters required</span>
              </div>
            </div>
            <div class="form-group">
              <label>Last Name<span class="required">*</span></label>
              <input type="text" formControlName="lastName" placeholder="Enter Last Name" class="form-input" maxlength="50">
              <div *ngIf="step1Form.get('lastName')?.invalid && step1Form.get('lastName')?.touched" class="error-message">
                <span *ngIf="step1Form.get('lastName')?.errors?.['required']">Last name is required</span>
                <span *ngIf="step1Form.get('lastName')?.errors?.['pattern']">Only letters and spaces are allowed</span>
                <span *ngIf="step1Form.get('lastName')?.errors?.['minlength']">Minimum 2 characters required</span>
              </div>
            </div>
          </div>
          <div class="form-row">
            <div class="form-group">
              <label>Cellphone Number<span class="required">*</span></label>
              <div class="input-with-verify">
                <input type="text" formControlName="cellphone" placeholder="Enter Cellphone Number" class="form-input" maxlength="10">
                <button type="button" class="verify-btn" [disabled]="!step1Form.get('cellphone')?.valid" (click)="sendOTP('mobile')">Verify</button>
              </div>
              <div *ngIf="step1Form.get('cellphone')?.invalid && step1Form.get('cellphone')?.touched" class="error-message">
                <span *ngIf="step1Form.get('cellphone')?.errors?.['required']">Mobile number is required</span>
                <span *ngIf="step1Form.get('cellphone')?.errors?.['pattern']">Please enter a valid 10-digit mobile number</span>
              </div>
              <div *ngIf="mobileOTPSent" class="otp-field">
                <input type="text" formControlName="mobileOTP" placeholder="Enter OTP" class="form-input otp-input" maxlength="6" (input)="onOTPInput('mobile', $event)">
                <button type="button" class="resend-btn" (click)="sendOTP('mobile')">Resend OTP</button>
              </div>
              <div *ngIf="step1Form.get('mobileOTP')?.invalid && step1Form.get('mobileOTP')?.touched" class="error-message">
                <span *ngIf="step1Form.get('mobileOTP')?.errors?.['required']">OTP is required</span>
                <span *ngIf="step1Form.get('mobileOTP')?.errors?.['pattern']">Please enter a valid 6-digit OTP</span>
              </div>
            </div>
            <div class="form-group">
              <label>Email<span class="required">*</span></label>
              <div class="input-with-verify">
                <input type="email" formControlName="email" placeholder="Enter Email" class="form-input">
                <button type="button" class="verify-btn" [disabled]="!step1Form.get('email')?.valid" (click)="sendOTP('email')">Verify</button>
              </div>
              <div *ngIf="step1Form.get('email')?.invalid && step1Form.get('email')?.touched" class="error-message">
                <span *ngIf="step1Form.get('email')?.errors?.['required']">Email is required</span>
                <span *ngIf="step1Form.get('email')?.errors?.['email']">Please enter a valid email address</span>
              </div>
              <div *ngIf="emailOTPSent" class="otp-field">
                <input type="text" formControlName="emailOTP" placeholder="Enter OTP" class="form-input otp-input" maxlength="6" (input)="onOTPInput('email', $event)">
                <button type="button" class="resend-btn" (click)="sendOTP('email')">Resend OTP</button>
              </div>
              <div *ngIf="step1Form.get('emailOTP')?.invalid && step1Form.get('emailOTP')?.touched" class="error-message">
                <span *ngIf="step1Form.get('emailOTP')?.errors?.['required']">OTP is required</span>
                <span *ngIf="step1Form.get('emailOTP')?.errors?.['pattern']">Please enter a valid 6-digit OTP</span>
              </div>
            </div>
          </div>
          <div class="form-row">
            <div class="form-group full-width">
              <label>Company Name<span class="required">*</span></label>
              <input type="text" formControlName="companyName" placeholder="Enter Company Name" class="form-input" maxlength="100">
              <div *ngIf="step1Form.get('companyName')?.invalid && step1Form.get('companyName')?.touched" class="error-message">
                <span *ngIf="step1Form.get('companyName')?.errors?.['required']">Company name is required</span>
                <span *ngIf="step1Form.get('companyName')?.errors?.['minlength']">Minimum 2 characters required</span>
              </div>
            </div>
          </div>
        </form>
      </div>

      <!-- Step 2: Address Information -->
      <div *ngIf="currentStep === 2" class="step-form">
        <form [formGroup]="step2Form">
          <div class="form-row">
            <div class="form-group">
              <label>Address<span class="required">*</span></label>
              <input type="text" formControlName="address" placeholder="Enter Address" class="form-input" maxlength="200">
              <div *ngIf="step2Form.get('address')?.invalid && step2Form.get('address')?.touched" class="error-message">
                <span *ngIf="step2Form.get('address')?.errors?.['required']">Address is required</span>
                <span *ngIf="step2Form.get('address')?.errors?.['minlength']">Minimum 5 characters required</span>
              </div>
            </div>
            <div class="form-group">
              <label>Region<span class="required">*</span></label>
              <select formControlName="region" class="form-select">
                <option value="">Select Region</option>
                <option value="pacific-coast">Pacific Coast</option>
                <option value="central">Central</option>
                <option value="north">North</option>
              </select>
              <div *ngIf="step2Form.get('region')?.invalid && step2Form.get('region')?.touched" class="error-message">
                <span *ngIf="step2Form.get('region')?.errors?.['required']">Region is required</span>
              </div>
            </div>
          </div>
          <div class="form-row">
            <div class="form-group">
              <label>Zone or Area<span class="required">*</span></label>
              <select formControlName="zone" class="form-select">
                <option value="">Select Zone</option>
                <option value="puebla">Puebla</option>
                <option value="mexico-city">Mexico City</option>
              </select>
              <div *ngIf="step2Form.get('zone')?.invalid && step2Form.get('zone')?.touched" class="error-message">
                <span *ngIf="step2Form.get('zone')?.errors?.['required']">Zone is required</span>
              </div>
            </div>
            <div class="form-group">
              <label>State<span class="required">*</span></label>
              <select formControlName="state" class="form-select">
                <option value="">Select State</option>
                <option value="baja-california">Baja California</option>
                <option value="sonora">Sonora</option>
              </select>
              <div *ngIf="step2Form.get('state')?.invalid && step2Form.get('state')?.touched" class="error-message">
                <span *ngIf="step2Form.get('state')?.errors?.['required']">State is required</span>
              </div>
            </div>
          </div>
          <div class="form-row">
            <div class="form-group">
              <label>City<span class="required">*</span></label>
              <select formControlName="city" class="form-select">
                <option value="">Select City</option>
                <option value="tijuana">Tijuana</option>
                <option value="mexicali">Mexicali</option>
              </select>
              <div *ngIf="step2Form.get('city')?.invalid && step2Form.get('city')?.touched" class="error-message">
                <span *ngIf="step2Form.get('city')?.errors?.['required']">City is required</span>
              </div>
            </div>
            <div class="form-group">
              <label>Main Crop<span class="required">*</span></label>
              <select formControlName="mainCrop" class="form-select">
                <option value="">Select Main Crop</option>
                <option value="corn">Corn</option>
                <option value="wheat">Wheat</option>
                <option value="rice">Rice</option>
                <option value="soybean">Soybean</option>
              </select>
              <div *ngIf="step2Form.get('mainCrop')?.invalid && step2Form.get('mainCrop')?.touched" class="error-message">
                <span *ngIf="step2Form.get('mainCrop')?.errors?.['required']">Main crop is required</span>
              </div>
            </div>
          </div>
        </form>
      </div>

      <!-- Step 3: Document Upload -->
      <div *ngIf="currentStep === 3" class="step-form">
        <div class="upload-section">
          <div class="upload-card" *ngFor="let doc of documentTypes">
            <div class="upload-icon">
              <svg width="48" height="48" viewBox="0 0 24 24" fill="none">
                <path d="M21 15V19C21 19.5304 20.7893 20.0391 20.4142 20.4142C20.0391 20.7893 19.5304 21 19 21H5C4.46957 21 3.96086 20.7893 3.58579 20.4142C3.21071 20.0391 3 19.5304 3 19V15M17 8L12 3M12 3L7 8M12 3V15" stroke="#9CA3AF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </div>
            <p>
              <span class="browse-link">Click to browse</span> or drag and drop
            </p>
            <small>{{ doc.description }}</small>
            <input type="file" class="file-input" (change)="onFileSelect($event, doc.key)" accept=".pdf,.jpg,.jpeg,.png">
            <div *ngIf="uploadedFiles[doc.key]" class="uploaded-file">
              ✓ {{ uploadedFiles[doc.key].name }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Footer -->
    <div class="signup-footer">
      <button class="next-btn" (click)="onNext()" [disabled]="!isCurrentStepValid()">
        {{ currentStep === 3 ? 'Submit' : 'Next' }}
      </button>
    </div>
  </div>
</div>

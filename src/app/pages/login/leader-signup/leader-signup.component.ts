import { Component, OnInit, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { MatDialogRef } from '@angular/material/dialog';
import { ToastrService } from 'ngx-toastr';

import { Utility } from '../../../shared/utility/utility';
import { UserService } from '../../../app-services/user-service';

@Component({
  selector: 'app-leader-signup',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule],
  templateUrl: './leader-signup.component.html',

  styleUrls: ['./leader-signup.component.scss']
})
export class LeaderSignupComponent implements OnInit {
  @Output() close = new EventEmitter<void>();

  currentStep = 1;
  step1Form!: FormGroup;
  step2Form!: FormGroup;
  uploadedFiles: { [key: string]: File } = {};
  mobileOTPSent = false;
  emailOTPSent = false;
  mobileOTPVerified = false;
  emailOTPVerified = false;

  documentTypes = [
    { key: 'tax', description: 'Upload Tax Document (PDF, JPG, PNG up to 10MB)' },
    { key: 'identity', description: 'Upload Identity Document (PDF, JPG, PNG up to 10MB)' },
    { key: 'address', description: 'Upload Address Document (PDF, JPG, PNG up to 10MB)' }
  ];

  constructor(
    private fb: FormBuilder,
    private toastr: ToastrService,
    private utility: Utility,
    private userService: UserService
  ) {
    this.initializeForms();
  }

  ngOnInit(): void {}

  initializeForms(): void {
    this.step1Form = this.fb.group({
      firstName: ['', [Validators.required, Validators.minLength(2), Validators.pattern(/^[a-zA-Z\s]+$/)]],
      lastName: ['', [Validators.required, Validators.minLength(2), Validators.pattern(/^[a-zA-Z\s]+$/)]],
      cellphone: ['', [Validators.required, Validators.pattern(/^\d{10}$/)]],
      email: ['', [Validators.required, Validators.email]],
      companyName: ['', [Validators.required, Validators.minLength(2)]],
      mobileOTP: [''],
      emailOTP: ['']
    });

    this.step2Form = this.fb.group({
      address: ['', [Validators.required, Validators.minLength(5)]],
      region: ['', [Validators.required]],
      zone: ['', [Validators.required]],
      state: ['', [Validators.required]],
      city: ['', [Validators.required]],
      mainCrop: ['', [Validators.required]]
    });
  }

  onBack(): void {
    if (this.currentStep > 1) {
      this.currentStep--;
    } else {
      this.close.emit();
    }
  }

  onNext(): void {
    if (this.currentStep < 3) {
      this.currentStep++;
    } else {
      this.onSubmit();
    }
  }

  isCurrentStepValid(): boolean {
    switch (this.currentStep) {
      case 1:
        const basicFieldsValid = (this.step1Form.get('firstName')?.valid ?? false) &&
                                (this.step1Form.get('lastName')?.valid ?? false) &&
                                (this.step1Form.get('cellphone')?.valid ?? false) &&
                                (this.step1Form.get('email')?.valid ?? false) &&
                                (this.step1Form.get('companyName')?.valid ?? false);

        // Check if OTP is sent and verified
        const mobileOTPValid = !this.mobileOTPSent || (this.mobileOTPVerified && (this.step1Form.get('mobileOTP')?.valid ?? false));
        const emailOTPValid = !this.emailOTPSent || (this.emailOTPVerified && (this.step1Form.get('emailOTP')?.valid ?? false));

        return basicFieldsValid && mobileOTPValid && emailOTPValid;
      case 2:
        return this.step2Form.valid;
      case 3:
        return Object.keys(this.uploadedFiles).length === 3;
      default:
        return false;
    }
  }

  sendOTP(type: 'mobile' | 'email'): void {
    const value = type === 'mobile' ? this.step1Form.get('cellphone')?.value : this.step1Form.get('email')?.value;

    if (!value) {
      this.toastr.error(`Please enter ${type === 'mobile' ? 'mobile number' : 'email'} first`);
      return;
    }

    // Use the same payload structure as login component
    const payload = {
      [type === 'mobile' ? 'mobileNo' : 'email']: value,
      isSignup: true
    };

    // Use the correct UserService methods like in login component
    const apiCall = type === 'email' ?
      this.userService.generateEmailOTPForLogin(payload) :
      this.userService.generateMobileOTPForLogin(payload);

    apiCall.subscribe({
      next: (response: any) => {
        try {
          const decrypted = this.utility.decrypt(response.encryptedBody || response);
          const result = JSON.parse(decrypted);
          this.toastr.success(`OTP sent to your ${type} successfully!`);
        } catch (error) {
          this.toastr.success(`OTP sent to your ${type} successfully!`);
        }

        if (type === 'mobile') {
          this.mobileOTPSent = true;
          this.step1Form.get('mobileOTP')?.setValidators([Validators.required, Validators.pattern(/^\d{6}$/)]);
          this.step1Form.get('mobileOTP')?.updateValueAndValidity();
        } else {
          this.emailOTPSent = true;
          this.step1Form.get('emailOTP')?.setValidators([Validators.required, Validators.pattern(/^\d{6}$/)]);
          this.step1Form.get('emailOTP')?.updateValueAndValidity();
        }
      },
      error: (error: any) => {
        try {
          const decryptedErr = this.utility.decrypt(error.error?.encryptedBody || error.error);
          const parsedErr = JSON.parse(decryptedErr);
          this.toastr.error(parsedErr.message || `Failed to send OTP to ${type}`);
        } catch {
          this.toastr.error(`Failed to send OTP to ${type}. Please try again.`);
        }
      }
    });
  }

  onOTPInput(type: 'mobile' | 'email', event: any): void {
    const value = event.target.value;
    if (value.length === 6) {
      this.validateOTP(type, value);
    }
  }

  validateOTP(type: 'mobile' | 'email', otp: string): void {
    const contactValue = type === 'mobile' ? this.step1Form.get('cellphone')?.value : this.step1Form.get('email')?.value;

    const payload = {
      [type === 'mobile' ? 'mobileNo' : 'email']: contactValue,
      otp: otp,
      isSignup: true
    };

    // Use the correct UserService method like in login component
    this.userService.validateOTPForLogin(payload).subscribe({
      next: (response: any) => {
        try {
          const decrypted = this.utility.decrypt(response.encryptedBody || response);
          const result = JSON.parse(decrypted);
          this.toastr.success(`${type === 'mobile' ? 'Mobile' : 'Email'} verified successfully!`);
        } catch (error) {
          this.toastr.success(`${type === 'mobile' ? 'Mobile' : 'Email'} verified successfully!`);
        }

        if (type === 'mobile') {
          this.mobileOTPVerified = true;
        } else {
          this.emailOTPVerified = true;
        }
      },
      error: (error: any) => {
        try {
          const decryptedErr = this.utility.decrypt(error.error?.encryptedBody || error.error);
          const parsedErr = JSON.parse(decryptedErr);
          this.toastr.error(parsedErr.message || 'Invalid OTP');
        } catch {
          this.toastr.error('Invalid OTP. Please try again.');
        }
        // Clear the OTP field
        this.step1Form.get(type === 'mobile' ? 'mobileOTP' : 'emailOTP')?.setValue('');
      }
    });
  }

  onFileSelect(event: any, type: string): void {
    const file = event.target.files[0];
    if (file) {
      this.uploadedFiles[type] = file;
      this.toastr.success(`${type} document uploaded successfully`);
    }
  }

  onSubmit(): void {
    const formData = {
      ...this.step1Form.value,
      ...this.step2Form.value,
      documents: this.uploadedFiles
    };
    
    console.log('Signup data:', formData);
    this.toastr.success('Registration submitted successfully!');
    this.close.emit();
  }

  onOverlayClick(event: Event): void {
    this.close.emit();
  }
}

import { Component, OnInit, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { MatDialogRef } from '@angular/material/dialog';
import { ToastrService } from 'ngx-toastr';

@Component({
  selector: 'app-leader-signup',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule],
  template: `
    <div class="signup-overlay" (click)="onOverlayClick($event)">
      <div class="signup-modal" (click)="$event.stopPropagation()">
        <!-- Header -->
        <div class="signup-header">
          <button class="back-btn" (click)="onBack()">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
              <path d="M19 12H5M12 19L5 12L12 5" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </button>
          <h2>Leader SignUp</h2>
        </div>

        <!-- Progress Steps -->
        <div class="progress-steps">
          <div class="step" [class.active]="currentStep >= 1" [class.completed]="currentStep > 1">
            <div class="step-icon">
              <img [src]="currentStep >= 1 ? 'assets/img/fill-contact.svg' : 'assets/img/unfill-contact.svg'" alt="Contact Step" width="24" height="24">
            </div>
          </div>
          <div class="step-line" [class.completed]="currentStep > 1"></div>
          <div class="step" [class.active]="currentStep >= 2" [class.completed]="currentStep > 2">
            <div class="step-icon">
              <img [src]="currentStep >= 2 ? 'assets/img/fill-address.svg' : 'assets/img/unfill-address.svg'" alt="Address Step" width="24" height="24">
            </div>
          </div>
          <div class="step-line" [class.completed]="currentStep > 2"></div>
          <div class="step" [class.active]="currentStep >= 3">
            <div class="step-icon">
              <img [src]="currentStep >= 3 ? 'assets/img/fill-document.svg' : 'assets/img/unfill-document.svg'" alt="Document Step" width="24" height="24">
            </div>
          </div>
        </div>

        <!-- Step Content -->
        <div class="step-content">
          <!-- Step 1: Personal Information -->
          <div *ngIf="currentStep === 1" class="step-form">
            <form [formGroup]="step1Form">
              <div class="form-row">
                <div class="form-group">
                  <label>First Name<span class="required">*</span></label>
                  <input type="text" formControlName="firstName" placeholder="Enter First Name" class="form-input">
                </div>
                <div class="form-group">
                  <label>Last Name<span class="required">*</span></label>
                  <input type="text" formControlName="lastName" placeholder="Enter Last Name" class="form-input">
                </div>
              </div>
              <div class="form-row">
                <div class="form-group">
                  <label>Cellphone Number<span class="required">*</span></label>
                  <div class="input-with-verify">
                    <input type="text" formControlName="cellphone" placeholder="Enter Cellphone Number" class="form-input">
                    <button type="button" class="verify-btn" [disabled]="!step1Form.get('cellphone')?.valid" (click)="sendOTP('mobile')">Verify</button>
                  </div>
                  <div *ngIf="mobileOTPSent" class="otp-field">
                    <input type="text" formControlName="mobileOTP" placeholder="Enter OTP" class="form-input otp-input" maxlength="6">
                    <button type="button" class="resend-btn" (click)="sendOTP('mobile')">Resend OTP</button>
                  </div>
                </div>
                <div class="form-group">
                  <label>Email<span class="required">*</span></label>
                  <div class="input-with-verify">
                    <input type="email" formControlName="email" placeholder="Enter Email" class="form-input">
                    <button type="button" class="verify-btn" [disabled]="!step1Form.get('email')?.valid" (click)="sendOTP('email')">Verify</button>
                  </div>
                  <div *ngIf="emailOTPSent" class="otp-field">
                    <input type="text" formControlName="emailOTP" placeholder="Enter OTP" class="form-input otp-input" maxlength="6">
                    <button type="button" class="resend-btn" (click)="sendOTP('email')">Resend OTP</button>
                  </div>
                </div>
              </div>
              <div class="form-row">
                <div class="form-group full-width">
                  <label>Company Name<span class="required">*</span></label>
                  <input type="text" formControlName="companyName" placeholder="Enter Company Name" class="form-input">
                </div>
              </div>
            </form>
          </div>

          <!-- Step 2: Address Information -->
          <div *ngIf="currentStep === 2" class="step-form">
            <form [formGroup]="step2Form">
              <div class="form-row">
                <div class="form-group">
                  <label>Address<span class="required">*</span></label>
                  <input type="text" formControlName="address" placeholder="Enter Address" class="form-input">
                </div>
                <div class="form-group">
                  <label>Region<span class="required">*</span></label>
                  <select formControlName="region" class="form-select">
                    <option value="">Select Region</option>
                    <option value="pacific-coast">Pacific Coast</option>
                    <option value="central">Central</option>
                    <option value="north">North</option>
                  </select>
                </div>
              </div>
              <div class="form-row">
                <div class="form-group">
                  <label>Zone or Area<span class="required">*</span></label>
                  <select formControlName="zone" class="form-select">
                    <option value="">Select Zone</option>
                    <option value="puebla">Puebla</option>
                    <option value="mexico-city">Mexico City</option>
                  </select>
                </div>
                <div class="form-group">
                  <label>State<span class="required">*</span></label>
                  <select formControlName="state" class="form-select">
                    <option value="">Select State</option>
                    <option value="baja-california">Baja California</option>
                    <option value="sonora">Sonora</option>
                  </select>
                </div>
              </div>
              <div class="form-row">
                <div class="form-group">
                  <label>City<span class="required">*</span></label>
                  <select formControlName="city" class="form-select">
                    <option value="">Select City</option>
                    <option value="tijuana">Tijuana</option>
                    <option value="mexicali">Mexicali</option>
                  </select>
                </div>
                <div class="form-group">
                  <label>Main Crop<span class="required">*</span></label>
                  <select formControlName="mainCrop" class="form-select">
                    <option value="">Select Main Crop</option>
                    <option value="corn">Corn</option>
                    <option value="wheat">Wheat</option>
                  </select>
                </div>
              </div>
            </form>
          </div>

          <!-- Step 3: Document Upload -->
          <div *ngIf="currentStep === 3" class="step-form">
            <div class="upload-section">
              <div class="upload-card">
                <div class="upload-icon">
                  <svg width="48" height="48" viewBox="0 0 24 24" fill="none">
                    <path d="M21 15V19C21 19.5304 20.7893 20.0391 20.4142 20.4142C20.0391 20.7893 19.5304 21 19 21H5C4.46957 21 3.96086 20.7893 3.58579 20.4142C3.21071 20.0391 3 19.5304 3 19V15M17 8L12 3M12 3L7 8M12 3V15" stroke="#FF8033" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                  </svg>
                </div>
                <p>Upload Tax Document, <span class="browse-link">Browse</span></p>
                <small>Upload Pdf, Jpeg Pdf</small>
                <input type="file" class="file-input" accept=".pdf,.jpg,.jpeg" (change)="onFileSelect($event, 'tax')">
              </div>
              
              <div class="upload-card">
                <div class="upload-icon">
                  <svg width="48" height="48" viewBox="0 0 24 24" fill="none">
                    <path d="M21 15V19C21 19.5304 20.7893 20.0391 20.4142 20.4142C20.0391 20.7893 19.5304 21 19 21H5C4.46957 21 3.96086 20.7893 3.58579 20.4142C3.21071 20.0391 3 19.5304 3 19V15M17 8L12 3M12 3L7 8M12 3V15" stroke="#FF8033" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                  </svg>
                </div>
                <p>Upload Identity Document, <span class="browse-link">Browse</span></p>
                <small>Upload Pdf, Jpeg Pdf</small>
                <input type="file" class="file-input" accept=".pdf,.jpg,.jpeg" (change)="onFileSelect($event, 'identity')">
              </div>
              
              <div class="upload-card">
                <div class="upload-icon">
                  <svg width="48" height="48" viewBox="0 0 24 24" fill="none">
                    <path d="M21 15V19C21 19.5304 20.7893 20.0391 20.4142 20.4142C20.0391 20.7893 19.5304 21 19 21H5C4.46957 21 3.96086 20.7893 3.58579 20.4142C3.21071 20.0391 3 19.5304 3 19V15M17 8L12 3M12 3L7 8M12 3V15" stroke="#FF8033" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                  </svg>
                </div>
                <p>Upload Address Document, <span class="browse-link">Browse</span></p>
                <small>Upload Pdf, Jpeg Pdf</small>
                <input type="file" class="file-input" accept=".pdf,.jpg,.jpeg" (change)="onFileSelect($event, 'address')">
              </div>
            </div>
          </div>
        </div>

        <!-- Footer -->
        <div class="signup-footer">
          <button class="next-btn" (click)="onNext()" [disabled]="!isCurrentStepValid()">
            {{ currentStep === 3 ? 'Submit' : 'Next' }}
          </button>
        </div>
      </div>
    </div>
  `,
  styleUrls: ['./leader-signup.component.scss']
})
export class LeaderSignupComponent implements OnInit {
  @Output() close = new EventEmitter<void>();
  
  currentStep = 1;
  step1Form!: FormGroup;
  step2Form!: FormGroup;
  uploadedFiles: { [key: string]: File } = {};
  mobileOTPSent = false;
  emailOTPSent = false;

  constructor(
    private fb: FormBuilder,
    private toastr: ToastrService
  ) {
    this.initializeForms();
  }

  ngOnInit(): void {}

  initializeForms(): void {
    this.step1Form = this.fb.group({
      firstName: ['', [Validators.required]],
      lastName: ['', [Validators.required]],
      cellphone: ['', [Validators.required, Validators.pattern(/^\d{10}$/)]],
      email: ['', [Validators.required, Validators.email]],
      companyName: ['', [Validators.required]],
      mobileOTP: [''],
      emailOTP: ['']
    });

    this.step2Form = this.fb.group({
      address: ['', [Validators.required]],
      region: ['', [Validators.required]],
      zone: ['', [Validators.required]],
      state: ['', [Validators.required]],
      city: ['', [Validators.required]],
      mainCrop: ['', [Validators.required]]
    });
  }

  onBack(): void {
    if (this.currentStep > 1) {
      this.currentStep--;
    } else {
      this.close.emit();
    }
  }

  onNext(): void {
    if (this.currentStep < 3) {
      this.currentStep++;
    } else {
      this.onSubmit();
    }
  }

  isCurrentStepValid(): boolean {
    switch (this.currentStep) {
      case 1:
        const basicFieldsValid = (this.step1Form.get('firstName')?.valid ?? false) &&
                                (this.step1Form.get('lastName')?.valid ?? false) &&
                                (this.step1Form.get('cellphone')?.valid ?? false) &&
                                (this.step1Form.get('email')?.valid ?? false) &&
                                (this.step1Form.get('companyName')?.valid ?? false);

        const mobileOTPValid = !this.mobileOTPSent || (this.step1Form.get('mobileOTP')?.valid ?? false);
        const emailOTPValid = !this.emailOTPSent || (this.step1Form.get('emailOTP')?.valid ?? false);

        return basicFieldsValid && mobileOTPValid && emailOTPValid;
      case 2:
        return this.step2Form.valid;
      case 3:
        return Object.keys(this.uploadedFiles).length === 3;
      default:
        return false;
    }
  }

  sendOTP(type: 'mobile' | 'email'): void {
    if (type === 'mobile') {
      this.mobileOTPSent = true;
      this.step1Form.get('mobileOTP')?.setValidators([Validators.required, Validators.pattern(/^\d{4,6}$/)]);
      this.step1Form.get('mobileOTP')?.updateValueAndValidity();
    } else {
      this.emailOTPSent = true;
      this.step1Form.get('emailOTP')?.setValidators([Validators.required, Validators.pattern(/^\d{4,6}$/)]);
      this.step1Form.get('emailOTP')?.updateValueAndValidity();
    }
    this.toastr.info(`OTP sent to your ${type}`);
  }

  onFileSelect(event: any, type: string): void {
    const file = event.target.files[0];
    if (file) {
      this.uploadedFiles[type] = file;
      this.toastr.success(`${type} document uploaded successfully`);
    }
  }

  onSubmit(): void {
    const formData = {
      ...this.step1Form.value,
      ...this.step2Form.value,
      documents: this.uploadedFiles
    };
    
    console.log('Signup data:', formData);
    this.toastr.success('Registration submitted successfully!');
    this.close.emit();
  }

  onOverlayClick(event: Event): void {
    this.close.emit();
  }
}

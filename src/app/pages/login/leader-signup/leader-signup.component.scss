.signup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.signup-modal {
  background: white;
  border-radius: 12px;
  width: 90%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.signup-header {
  background: #FF8033;
  color: white;
  padding: 1rem 1.5rem;
  border-radius: 12px 12px 0 0;
  display: flex;
  align-items: center;
  gap: 1rem;

  .back-btn {
    background: none;
    border: none;
    color: white;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.2s;

    &:hover {
      background: rgba(255, 255, 255, 0.1);
    }
  }

  h2 {
    margin: 0;
    font-size: 1.5rem;
    font-weight: 600;
  }
}

.progress-steps {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem 1.5rem 1rem;
  gap: 1rem;

  .step {
    display: flex;
    align-items: center;
    justify-content: center;

    .step-icon {
      width: 60px;
      height: 60px;
      border-radius: 50%;
      background: #E5E7EB;
      color: #9CA3AF;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.3s ease;
    }

    &.active .step-icon {
      background: #FF8033;
      color: white;
    }

    &.completed .step-icon {
      background: #FF8033;
      color: white;
    }
  }

  .step-line {
    width: 80px;
    height: 3px;
    background: #E5E7EB;
    transition: background-color 0.3s ease;

    &.completed {
      background: #FF8033;
    }
  }
}

.step-content {
  padding: 0 1.5rem 1rem;
}

.step-form {
  .form-row {
    display: flex;
    gap: 1rem;
    margin-bottom: 1.5rem;

    @media (max-width: 768px) {
      flex-direction: column;
      gap: 1rem;
    }
  }

  .form-group {
    flex: 1;

    &.full-width {
      flex: 1 1 100%;
    }

    label {
      display: block;
      margin-bottom: 0.5rem;
      font-weight: 500;
      color: #374151;
      font-size: 0.9rem;
    }

    .form-input,
    .form-select {
      width: 100%;
      padding: 0.75rem;
      border: 1px solid #D1D5DB;
      border-radius: 6px;
      font-size: 0.9rem;
      transition: border-color 0.2s;

      &:focus {
        outline: none;
        border-color: #FF8033;
        box-shadow: 0 0 0 3px rgba(255, 107, 53, 0.1);
      }

      &::placeholder {
        color: #9CA3AF;
      }
    }

    .input-with-verify {
      display: flex;
      gap: 0.5rem;

      .form-input {
        flex: 1;
      }

      .verify-btn {
        background: #FF8033;
        color: white;
        border: none;
        padding: 0.75rem 1rem;
        border-radius: 6px;
        font-size: 0.8rem;
        font-weight: 500;
        cursor: pointer;
        transition: background-color 0.2s;
        white-space: nowrap;

        &:hover:not(:disabled) {
          background: #E55A2B;
        }

        &:disabled {
          background: #D1D5DB;
          cursor: not-allowed;
        }
      }
    }

    .otp-field {
      margin-top: 0.5rem;
      display: flex;
      gap: 0.5rem;

      .otp-input {
        flex: 1;
        font-size: 0.9rem;
      }

      .resend-btn {
        background: transparent;
        color: #FF8033;
        border: 1px solid #FF8033;
        padding: 0.5rem 0.75rem;
        border-radius: 6px;
        font-size: 0.8rem;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s;
        white-space: nowrap;

        &:hover {
          background: #FF8033;
          color: white;
        }
      }
    }
  }
}

.upload-section {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  gap: 1rem;
  padding: 1rem 0;

  .upload-card {
    border: 2px dashed #D1D5DB;
    border-radius: 8px;
    padding: 1.5rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.2s;
    position: relative;

    &:hover {
      border-color: #FF8033;
      background: rgba(255, 107, 53, 0.02);
    }

    .upload-icon {
      margin-bottom: 1rem;
      display: flex;
      justify-content: center;
    }

    p {
      margin: 0 0 0.5rem;
      font-size: 0.9rem;
      color: #374151;

      .browse-link {
        color: #FF8033;
        font-weight: 500;
      }
    }

    small {
      color: #6B7280;
      font-size: 0.8rem;
    }

    .file-input {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      opacity: 0;
      cursor: pointer;
    }
  }
}

.signup-footer {
  padding: 1rem 1.5rem 1.5rem;
  display: flex;
  justify-content: center;

  .next-btn {
    background: #FF8033;
    color: white;
    border: none;
    padding: 0.75rem 3rem;
    border-radius: 6px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s;
    min-width: 120px;

    &:hover:not(:disabled) {
      background: #E55A2B;
    }

    &:disabled {
      background: #D1D5DB;
      cursor: not-allowed;
    }
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .signup-modal {
    width: 95%;
    margin: 1rem;
  }

  .progress-steps {
    padding: 1.5rem 1rem 1rem;

    .step .step-icon {
      width: 50px;
      height: 50px;
    }

    .step-line {
      width: 60px;
    }
  }

  .upload-section {
    grid-template-columns: 1fr;
  }
}

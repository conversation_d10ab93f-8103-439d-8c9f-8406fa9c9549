@import "../../theme/sass/auth";
@import "../../../styles";
@import "../../theme/sass/conf/variables";

.login-container {
  background-image: url(../../../assets/img/lider_okta_screen.png);
  height: 100vh;
  padding: 0;
  margin: 0;
  background-position: top;
  background-size: cover;
  position: relative;
  overflow: hidden;
  text-align: center;

  &::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    z-index: 0;
    pointer-events: none; 
  }

  .language-change-button-container {
    position: relative;
    z-index: 1;
    text-align: right;
    margin: 10px 20px 0 0;

    .language-change-button {
      background-color: transparent;
      border: none;
      padding: 6px 14px;
      display: inline-flex;
      align-items: center;
      gap: 6px;
      font-family: "Poppins", sans-serif;
      font-size: 17px;
      font-weight: 700;
      cursor: pointer;

      .text-label {
        color: #ffffff;
        font-weight: 700;
        text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.6);
      }

      .arrow-icon {
        font-size: 25px;
        color: #ffffff;
      }
    }

    .language-dropdown {
      position: absolute;
      z-index: 2;
      background: white;
      color: black;
    }
  }
  .welcome-element-container {
    display: inline;
    position: absolute;
    transform: translate(-45%, -25%);
    left: 20%;
    top: 50%;
    height: 50%;
    width: 100%;
    z-index: 1;
    .center-container {
      #welcome-para {
        font-family: $sans-font-family;
        font-size: 44px;
        font-weight: 100 !important;
        color: #eff5ff;
        opacity: 95%;
        padding: 0px 280px 0px 90px;
      }
      #heading-one {
        font-family: "Poppins", sans-serif;
        color: #fff;
        font-weight: 800;
        font-size: 50px;
        text-shadow: 2px 8px 4px rgba(0, 0, 0, 0.8);
      }
    }
  }

  .element-container {
    z-index: 1;
    width: 50%;
    .background .shape {
      z-index: 1;

      height: 200px;
      width: 200px;
      position: absolute;
      border-radius: 50%;
    }
    form {
      z-index: 1;
      height: auto;
      width: 450px;
      padding: 0px 20px;
      background-color: #fff;
      position: absolute;
      transform: translate(-50%, -50%);
      top: 50%;
      left: 65%;
      border-radius: 20px;
      backdrop-filter: blur(10px);
      border: 2px solid #FF6F1F;
      .logo-container {
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 15px;
        margin-top: 6px;
        width: 100%;

        .upl-logo {
          max-width: 190px;
          height: auto;
          object-fit:cover;
          object-position: center;
        }
      }

      .user-type-toggle {
        display: flex;
        justify-content: center;
        align-items: center;
        margin-top: 15px;
        width: 90%;
        margin-left: auto;
        margin-right: auto;
        border: 1px solid #e8e8e8;
        border-radius: 25px;
        overflow: hidden;
        p{
          margin-top: 10px;
        }

        .toggle-btn {
          padding: 10px 18px;
          border: none;
          font-family: "Poppins", sans-serif;
          font-size: 17px;
          color: #000;
          font-weight: 500;
          cursor: pointer;
          flex: 1;
          text-align: center;
          border-radius: 23px;
          transition: all 0.2s ease;
          background-color: transparent;
          &:hover:not(.active) {
            color: #666666;
          }

          &.active {
            background-color: #FF6F1F;
            color: #ffffff;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12);
            transform: scale(1.02);
          }
        }
      }

      // Smooth content transitions
      .login-content {
        padding-bottom: 25px;

        .leader-content,
        .employee-content {
          margin-bottom: 20px;
          transform: translateY(20px);
          transition: all 0.4s ease;
        }

        // Only add top padding for UPL Employee tab
        .employee-content {
          padding-top:25px;
        }
      }

      // Leader tab specific styling
      .leader-content {

        button.otp-button {
          width: 90%;
          padding: 13px;
          background-color: #FF6F1F !important;
          color: #ffffff !important;
          border: none !important;
          border-radius: 8px;
          font-family: "Poppins", sans-serif !important;
          font-size: 16px !important;
          font-weight: 600 !important;
          cursor: pointer;
          transition: all 0.3s ease;
          margin-bottom: 20px;
          margin-left: 20px;
          margin-top: 20px;
          text-align: center !important;
          display: block;
          line-height: normal !important;

          &:disabled {
            opacity: 0.5;
            cursor: not-allowed;
          }
        }
        .input-group {
          margin-left: 22px;

          label {
            display: block;
            font-size: 18px;
            font-weight: 500;
            color: #000;
            margin-bottom: 5px;
            text-align: left;
            margin-left: 1px;
          }

          .form-input {
            width: 90%;
            margin-left: 26px;
            padding: 10px 16px;
            border: 1px solid #e0e0e0;
            border-radius: 8px !important;
            font-weight: 500;
            font-size: 14px;
            color: #333333;
            background-color: #ffffff;
            transition: all 0.3s ease;
            box-sizing: border-box;

            &:focus {
              outline: none;
              border-color: #FF6F1F;
              box-shadow: 0 0 0 2px rgba(255, 111, 31, 0.1);
              border-radius: 8px !important;
            }

            &::placeholder {
              color: #999999;
              font-size: 14px;
            }

            &.error {
              border-color: #dc3545;
              box-shadow: 0 0 0 2px rgba(220, 53, 69, 0.1);
              border-radius: 8px !important;
            }

            &:disabled {
              opacity: 0.5;
              cursor: not-allowed;
            }
          }

          .error-message {
            color: #dc3545;
            font-size: 12px;
            font-family: "Poppins", sans-serif;
            margin-top: 5px;
            margin-left: 26px;
            font-weight: 400;
          }
        }



        .signup-text {
          text-align: center;
          font-size: 14px;
          color: #666666;
          margin: 15px 0 10px;
        }

        .signup-button {
          width: 90%;
          padding: 10px;
          background-color: #FF6F1F;
          color: #ffffff;
          border: none;
          border-radius: 8px;
          font-size: 16px;
          font-weight: 600;
          cursor: pointer;
          transition: all 0.3s ease;

        }
      }

      // Animation keyframes
      @keyframes fadeInUp {
        from {
          opacity: 0;
          transform: translateY(20px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      .sign-in-button {
        color: #ffffff;
        background-color: #FF6F1F;
        padding: 10px;
        border-radius: 10px;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 18px;
        width: 90%;
        margin: 0px 0px 0px 20px;
        font-family: $sans-font-family;
        font-weight:400;
      }
      #para {
        color: #000000;
        font-size: 22px;
        font-family: $sans-font-family;
      }
    }
    form * {
      font-family: "Poppins", sans-serif;
      color: #ffffff;
      letter-spacing: 0.5px;
      outline: none;
      border: none;
      font-weight: 500;
      font-size: 22px;
    }
    form h1 {
      font-size: 32px;
      font-weight: 800;
      line-height: 42px;
      text-align: center;
    }

    label {
      display: block;
      margin-top: 10px;
      font-size: 18px;
      color: #000;
      font-weight: 500;
    }
    input {
      display: block;
      height: 50px;
      width: 100%;
      background-color: rgba(255, 255, 255, 0.07);
      border-radius: 3px;
      padding: 0 10px;
      margin-top: 8px;
      font-size: 14px;
      font-weight: 300;
    }
    ::placeholder {
      color: #e5e5e5;
    }
    button {
      width: 100%;
      background-color: #ffffff;
      color: #080710;
      padding: 15px 0;
      font-size: 18px;
      font-weight: 600;
      border-radius: 5px;
      cursor: pointer;
    }
    .credit {
      margin: 10px;
    }
  }
}

.example-margin .mat-checkbox-background.mat-accent {
  background-color: #FF8033 !important;
}

.login-body {
  .has-error .form-control {
    border: 1px solid #dcdcdc !important;
  }
  .has-success .form-control {
    border: 1px solid #FF8033;
  }
  .sign-up-error {
    float: left;
    margin-top: -30px;
    margin-bottom: 0px;
    width: 100%;
  }
  .help-block {
    color: red;
  }
  .sub-little-error {
    font-size: 11px;
  }
}


.login-page {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  background-color: #f5f5f5;
  .centered-video {
    max-height: 210px;
    border: none;
    border-radius: 0;
    box-shadow: none;
    mix-blend-mode: multiply;
  }
  .text{
    margin: 10px;

  }
}


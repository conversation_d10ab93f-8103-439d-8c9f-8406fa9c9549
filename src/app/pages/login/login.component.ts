import {
  Component,
  ViewEncapsulation,
  ChangeDetectorRef
} from '@angular/core';
import {
  FormGroup,
  FormBuilder,
  Validators,
  FormsModule,
  ReactiveFormsModule
} from '@angular/forms';
import {
  ActivatedRoute,
  Router,
  RouterModule
} from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import {
  BaMenuService,
  BaThemePreloader,
  BaThemeSpinner
} from '../../theme/services';
import { AppConstant } from '../../constants/app.constant';
import { UserService } from '../../app-services/user-service';
import { AuthenticationHelper } from '../../helpers/authentication';
import { EmailValidator } from '../../theme/validators/email.validator';
import { BlankSpaceValidator } from '../../theme/validators/blank.validator';
import {
  NgIf,
  CommonModule
} from '@angular/common';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatIconModule } from '@angular/material/icon';
import { DashboardService } from 'src/app/app-services/dashboard.service';
import { Utility } from 'src/app/shared/utility/utility';
import { NgImageSliderModule } from 'ng-image-slider';
import { SsoService } from 'src/app/app-services/sso.service';
import { AdminService } from 'src/app/app-services/admin.service';
import { MatMenuModule } from '@angular/material/menu';
import { MatRadioModule } from '@angular/material/radio';
import { TranslateService, TranslateModule } from '@ngx-translate/core';
import { AppTranslateService } from 'src/app/app-services/translate.service';
import { LoginTypeService } from '../../shared/services/login-type.service';
import { LeaderSignupComponent } from './leader-signup/leader-signup.component';

@Component({
  selector: 'login',
  templateUrl: './login.html',
  styleUrls: ['./login.scss'],
  encapsulation: ViewEncapsulation.None,
  standalone: true,
  imports: [
    FormsModule,
    ReactiveFormsModule,
    NgIf,
    MatCheckboxModule,
    CommonModule,
    MatIconModule,
    RouterModule,
    NgImageSliderModule,
    MatMenuModule,
    MatRadioModule,
    TranslateModule,
    LeaderSignupComponent
  ],
  providers: [BaThemePreloader, BaThemeSpinner, BaMenuService]
})
export class Login {
  allRequests: any[] = [];
  loyaltyImage: string = AppConstant.LOYALTY_IMAGE;
  form: FormGroup;
  submitted = false;
  hide = true;
  regionCode!: number;
  territoryCode!: number;
  randomNumber = Math.floor(1000 + Math.random() * 9000);
  notSignInScreen = false;
  isMenuOpen = false;
  selectedLanguage = 'en';
  changedLanguage = '';
  islogin: boolean = false;
  selectedUserType: string = 'employee'; // Default to UPL Employee
  leaderForm: FormGroup;
  showOTPField: boolean = false;
  otpSent: boolean = false;
  showSignupPopup: boolean = false;

  constructor(
    private fb: FormBuilder,
    private router: Router,
    private routes: ActivatedRoute,
    private toastr: ToastrService,
    private spinner: BaThemeSpinner,
    private userService: UserService,
    private dashboardService: DashboardService,
    private utility: Utility,
    private ssoService: SsoService,
    private adminService: AdminService,
    private translate: TranslateService,
    private appTranslateService: AppTranslateService,
    private cdr: ChangeDetectorRef,
    private loginTypeService: LoginTypeService
  ) {
    this.spinner.hide();
    this.form = this.fb.group({
      email: ['', [Validators.required, EmailValidator.validate]],
      password: ['', [Validators.required, BlankSpaceValidator.validate, Validators.minLength(6), Validators.maxLength(16)]]
    });

    // Leader form for mobile/email and OTP
    this.leaderForm = this.fb.group({
      mobileOrEmail: ['', [Validators.required, this.mobileOrEmailValidator]],
      otp: ['', [Validators.required, Validators.pattern(/^\d{4,6}$/)]]
    });

    this.checkAndLoginUser();
  }

  ngOnInit(): void {
    this.selectedLanguage = 'en';
    this.changedLanguage = 'English';
    this.translate.use('en');
    localStorage.setItem('currentLanguage', this.utility.encrypt('en'));
    localStorage.setItem('selectedLanguage', this.utility.encrypt('English'));
  }

  onLanguageChange(event: any): void {
    const language = event.value;
    const selectedLangName = this.getLanguageName(language);
    this.selectLanguage(language, selectedLangName);
  }

  selectLanguage(language: string, selectedLanguageName: string): void {
    this.selectedLanguage = language;
    this.translate.use(language);
    localStorage.setItem('selectedLanguage', selectedLanguageName);
    localStorage.setItem('currentLanguage', language);
    this.changedLanguage = selectedLanguageName;
  }

  getLanguageName(code: string): string {
    switch (code) {
      case 'en': return 'English';
      case 'es': return 'Español';
      default: return 'English';
    }
  }

  toggleMenu(): void {
    this.isMenuOpen = !this.isMenuOpen;
  }

  menuClosed(): void {
    this.isMenuOpen = false;
  }



  onGetOTP(): void {
    if (!this.otpSent) {
      // Generate OTP
      if (this.leaderForm.get('mobileOrEmail')?.valid) {
        this.spinner.show();
        const inputValue = this.leaderForm.value.mobileOrEmail.trim();

        // Check if it's email or mobile
        const isEmail = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(inputValue);

        // Use unified OTP generation method
        this.generateOTP(inputValue, isEmail);
      }
    } else {
      // Verify OTP
      this.verifyOTP();
    }
  }

  generateOTP(inputValue: string, isEmail: boolean): void {
    const payload = isEmail ?
      { email: inputValue, isSignup: false } :
      { mobileNo: inputValue, isSignup: false };

    // Call different API based on input type
    const apiCall = isEmail ?
      this.userService.generateEmailOTPForLogin(payload) :
      this.userService.generateMobileOTPForLogin(payload);

    apiCall.subscribe({
      next: (response: any) => {
        this.spinner.hide();
        try {
          const decrypted = this.utility.decrypt(response.encryptedBody || response);
          const result = JSON.parse(decrypted);

          // Check if API response indicates success
          if (result.success === false || result.error === true) {
            // API returned error in success response
            this.toastr.error(result.message || 'Failed to send OTP. Please retry!');
            return;
          }

          // Success case
          this.toastr.success(`OTP sent to your ${isEmail ? 'email' : 'mobile'} successfully!`);
          this.showOTPField = true;
          this.otpSent = true;
        } catch (error) {
          // If decryption fails but we got a response, try to handle it
          try {
            const result = JSON.parse(response);
            if (result.success === false || result.error === true) {
              this.toastr.error(result.message || 'Failed to send OTP. Please retry!');
              return;
            }
          } catch (parseError) {
            // If all parsing fails, assume success for backward compatibility
            this.toastr.success(`OTP sent to your ${isEmail ? 'email' : 'mobile'} successfully!`);
            this.showOTPField = true;
            this.otpSent = true;
          }
        }
      },
      error: (error: any) => {
        this.spinner.hide();
        try {
          // Try to decrypt and parse the error response
          const decryptedErr = this.utility.decrypt(error.error?.encryptedBody || error.error);
          const parsedErr = JSON.parse(decryptedErr);
          // Show the actual error message from backend
          this.toastr.error(parsedErr.message || parsedErr.error || 'Failed to send OTP');
        } catch {
          // If decryption fails, try to parse the error directly
          try {
            const directError = JSON.parse(error.error);
            this.toastr.error(directError.message || directError.error || 'Failed to send OTP');
          } catch {
            // If all parsing fails, try to get any message from the error object
            const errorMessage = error.error?.message || error.message || 'Failed to send OTP. Please try again.';
            this.toastr.error(errorMessage);
          }
        }
      }
    });
  }

  verifyOTP(): void {
    if (this.leaderForm.valid) {
      this.spinner.show();
      const inputValue = this.leaderForm.value.mobileOrEmail.trim();
      const otpValue = this.leaderForm.value.otp.trim();

      // Check if it's email or mobile
      const isEmail = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(inputValue);

      const payload = isEmail ?
        { email: inputValue, otp: otpValue, isSignup: false } :
        { mobileNo: inputValue, otp: otpValue, isSignup: false };

      this.userService.validateOTPForLogin(payload).subscribe({
        next: (response: any) => {
          this.spinner.hide();
          try {
            const decrypted = this.utility.decrypt(response.encryptedBody || response);
            const result = JSON.parse(decrypted);

            // Check if API response indicates success
            if (result.success === false || result.error === true) {
              // API returned error in success response
              this.toastr.error(result.message || 'Invalid OTP. Please retry!');
              return;
            }

            // Success case - proceed with authentication
            this.setupLeaderAuthentication(result, inputValue);
            this.toastr.success('OTP verified successfully!');
            this.showSplashAndNavigate();

          } catch (error) {
            // If decryption fails but we got a response, try to handle it
            try {
              const result = JSON.parse(response);
              if (result.success === false || result.error === true) {
                this.toastr.error(result.message || 'Invalid OTP. Please retry!');
                return;
              }
            } catch (parseError) {
              // If all parsing fails, assume success for backward compatibility
              this.toastr.success('OTP verified successfully!');
              this.setupLeaderAuthentication(null, inputValue);
              this.showSplashAndNavigate();
            }
          }
        },
        error: (error: any) => {
          this.spinner.hide();
          try {
            // Try to decrypt and parse the error response
            const decryptedErr = this.utility.decrypt(error.error?.encryptedBody || error.error);
            const parsedErr = JSON.parse(decryptedErr);
            // Show the actual error message from backend
            this.toastr.error(parsedErr.message || parsedErr.error || 'Invalid OTP');
          } catch {
            // If decryption fails, try to parse the error directly
            try {
              const directError = JSON.parse(error.error);
              this.toastr.error(directError.message || directError.error || 'Invalid OTP');
            } catch {
              // If all parsing fails, try to get any message from the error object
              const errorMessage = error.error?.message || error.message || 'Invalid OTP. Please try again.';
              this.toastr.error(errorMessage);
            }
          }
        }
      });
    }
  }

  // Set up authentication for Leader login
  setupLeaderAuthentication(result: any, inputValue: string): void {
    // Set login type using the service
    this.loginTypeService.setLoginType('leader');

    // Generate a temporary token for Leader login
    const tempToken = 'leader_' + Date.now() + '_' + Math.random().toString(36).substring(2, 11);

    // Set authentication data
    AuthenticationHelper.setToken(tempToken);

    // Set user details from result or use defaults
    if (result && result.user) {
      AuthenticationHelper.setUsername(result.user.firstName || 'Leader');
      AuthenticationHelper.setEmail(result.user.email || inputValue);
      AuthenticationHelper.setUserID(result.user.id || 'leader_user');
      AuthenticationHelper.setRoleID(result.user.roleId || 1);
    } else {
      // Set default values for Leader
      AuthenticationHelper.setUsername('Leader');
      AuthenticationHelper.setEmail(inputValue);
      AuthenticationHelper.setUserID('leader_user');
      AuthenticationHelper.setRoleID(1); // Default to ADMIN role
    }

    // Store token in localStorage for auth interceptor
    localStorage.setItem('token', tempToken);
  }

  // Show splash screen and then navigate to dashboard
  showSplashAndNavigate(): void {
    // Show splash screen
    this.notSignInScreen = true;

    // Navigate to dashboard after 3 seconds
    setTimeout(() => {
      // Hide splash screen first
      this.notSignInScreen = false;

      // Then navigate to dashboard
      this.router.navigate(['dashboard']).then(() => {
        console.log('Navigation to dashboard completed');
      }).catch((error) => {
        console.error('Navigation failed:', error);
        // If navigation fails, hide splash screen anyway
        this.notSignInScreen = false;
      });
    }, 3000);
  }



  onSignUp(): void {
    this.showSignupPopup = true;
  }

  onCloseSignup(): void {
    this.showSignupPopup = false;
  }

  // Custom validator for mobile number or email
  mobileOrEmailValidator(control: any) {
    if (!control.value || control.value.trim() === '') {
      return { required: true }; // Return error for empty values
    }

    const value = control.value.trim();

    // Email regex pattern
    const emailPattern = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;

    // Mobile number pattern (any 10 digits)
    const mobilePattern = /^(\+91)?\d{10}$/;

    // Check if it's a mobile number (starts with digit or +91)
    if (/^(\+91)?\d+$/.test(value)) {
      // Validate mobile pattern (any 10 digits allowed)
      if (mobilePattern.test(value)) {
        return null; // Valid mobile
      }
      return { invalidMobileOrEmail: true }; // Invalid mobile
    }

    // Check if it's a valid email
    if (emailPattern.test(value)) {
      return null; // Valid email
    }

    return { invalidMobileOrEmail: true }; // Invalid format
  }

  // Handle input change with mobile number length restriction
  onMobileOrEmailInput(event: any): void {
    let value = event.target.value;

    // If it looks like a mobile number (starts with digit or +), restrict length
    if (/^(\+91)?\d/.test(value)) {
      // Remove any non-digit characters except +
      value = value.replace(/[^\d+]/g, '');

      // If starts with +91, allow +91 + 10 digits
      if (value.startsWith('+91')) {
        if (value.length > 13) {
          value = value.substring(0, 13);
        }
      } else {
        // For regular mobile numbers, limit to 10 digits (allow any 10 digits)
        if (value.length > 10) {
          value = value.substring(0, 10);
        }
      }

      event.target.value = value;
    }

    // Update form and trigger validation
    this.leaderForm.patchValue({ mobileOrEmail: value });
    this.leaderForm.get('mobileOrEmail')?.markAsTouched();
  }

  // Get error message for mobile/email field
  getMobileOrEmailError(): string {
    const control = this.leaderForm.get('mobileOrEmail');
    if (control?.hasError('required')) {
      return 'Mobile number or email is required';
    }
    if (control?.hasError('invalidMobileOrEmail')) {
      const value = control.value?.trim() || '';
      if (/^(\+91)?\d+$/.test(value)) {
        return 'Please enter a valid 10-digit mobile number';
      }
      return 'Please enter a valid email address';
    }
    return '';
  }

  // Check if form is valid for button state
  isGetOTPDisabled(): boolean {
    const mobileOrEmailValue = this.leaderForm.get('mobileOrEmail')?.value?.trim();
    const otpValue = this.leaderForm.get('otp')?.value?.trim();

    if (!this.otpSent) {
      // For Get OTP button - only check mobile/email field
      return !mobileOrEmailValue || (this.leaderForm.get('mobileOrEmail')?.invalid ?? true);
    } else {
      // For Verify OTP button - check both fields
      return !mobileOrEmailValue || !otpValue || (this.leaderForm.get('mobileOrEmail')?.invalid ?? true) || (this.leaderForm.get('otp')?.invalid ?? true);
    }
  }

  // Get button text based on state
  getButtonText(): string {
    if (this.otpSent) {
      return 'Verify OTP';
    } else {
      return 'Get OTP';
    }
  }

  // Handle tab switching and clear all data
  selectUserType(userType: string): void {
    this.selectedUserType = userType;
    this.clearAllData();
  }

  // Clear all form data and states
  clearAllData(): void {
    // Reset Leader form
    this.leaderForm.reset();

    // Reset OTP states
    this.otpSent = false;
    this.showOTPField = false;

    // Clear login type using the service
    this.loginTypeService.clearLoginType();

    // Reset form validation states
    this.leaderForm.markAsUntouched();
    this.leaderForm.markAsPristine();

    // Force change detection to update button text
    this.cdr.detectChanges();
  }

  checkAndLoginUser(): void {
    const params = this.routes.snapshot.queryParams;
    if (params['code']) {
      this.fetchUserTokenAndAuthenticate(params['code']);
    } else if (params['error']) {
      this.toastr.error('User Not Found');
    }
  }

  onSignIn(): void {
    // Set login type using the service
    this.loginTypeService.setLoginType('employee');
    this.ssoService.ssoLogin();
  }

  fetchUserTokenAndAuthenticate(code: string): void {
    this.notSignInScreen = true;
    this.spinner.show();
    const encryptedPayload = this.utility.encrypt(JSON.stringify({ code }));
    this.adminService.fetchUserTokenUsingSsoInfo({ encryptedBody: encryptedPayload }).subscribe({
      next: (res: any) => {
        const decrypted = this.utility.decrypt(res.encryptedBody);
        const parsed = JSON.parse(decrypted);
        AuthenticationHelper.setUsername(parsed.customer?.firstName || '');
        AuthenticationHelper.setEmail(parsed.customer?.email || '');
        const userRole = parsed.customer?.role || parsed.role || 'ADMIN';
        AuthenticationHelper.setRole(userRole);
  
        let roleId = 1;
        this.userService.fetchUserRole();
        switch (userRole) {
          case 'ADMIN': roleId = 1; break;
          case 'TERRITORY_MANAGER': roleId = 2; break;
          case 'REGIONAL_MANAGER': roleId = 3; break;
          case 'ZONAL_MANAGER': roleId = 4; break;
          case 'SBO': roleId = 5; break;
          case 'SBU': roleId = 6; break;
          default: roleId = 1;
        }
  
        AuthenticationHelper.setRoleID(roleId);
        AuthenticationHelper.setUserID(parsed.customer?.id || '');
  
        // Navigate
        switch (roleId) {
          case 1:
          case 2:
          case 3:
          case 4:
            this.router.navigate(['dashboard']);
            break;
          case 5:
            this.router.navigate(['zones']);
            break;
          case 6:
            this.router.navigate(['sbu']);
            break;
          default:
            this.router.navigate(['dashboard']);
        }
  
        this.fetchUserTokenSuccess(parsed);
      },
      error: (err) => {
        try {
          const decryptedErr = this.utility.decrypt(err.error?.encryptedBody || err.error);
          const parsedErr = JSON.parse(decryptedErr);
          this.toastr.error(parsedErr.message);
          this.errorHandler(parsedErr);
        } catch {
          this.toastr.error('Something went wrong');
          this.errorHandler(err);
        }
  
        this.spinner.hide();
        this.notSignInScreen = false;
        this.router.navigate(['sign-in']);
        this.errorHandler(err);
      }
    });
  }

  fetchUserTokenSuccess(res: any): void {
    let userToken = this.utility.encryptString(res.token)    
    const obj = {
      error: res.error,
      success: res.success,
      token:  res.token
    };
    let userTokenObj = this.utility.encryptString(obj)
    localStorage.setItem('userToken', userTokenObj.encryptedBody);
    localStorage.setItem('token', res.token);
    AuthenticationHelper.setToken(res.token);
    if(this.islogin){
      this.fetchAuthenticatedUserDetails(res.token);
      this.islogin = true;
    }
    this.toastr.success('Successfully logged in!');
  }

  fetchAuthenticatedUserDetails(token: string): void {
    this.spinner.show();
    this.adminService.fetchUserCurrentDetails(token).subscribe({
      next: (res) => {
        res = this.utility.decryptString(res)
        this.spinner.hide();
        const decrypted = this.utility.decrypt(res).slice(0, -1);
        const user = JSON.parse(decrypted);
        this.handleAuthentication(user);
      },
      error: () => {
        this.spinner.hide();
      }
    });
  }

  ngOnDestroy(): void {
    this.allRequests.forEach(req => req?.unsubscribe?.());
  }

  togglePassword(): void {
    this.hide = !this.hide;
  }

  onSubmit(values: any): void {
    this.spinner.show();
    this.submitted = true;

    // Prepare the encrypted payload as string
    const encryptedPayload = this.utility.encrypt(
      JSON.stringify({
        email: values.email.trim(),
        password: values.password.trim(),
      })
    );

    // If your backend expects the encrypted payload wrapped in an object:
    const payload = { encryptedBody: encryptedPayload };

    this.userService.login(payload).subscribe({
      next: (res: any) => {
        try {
          // Decrypt the response — adjust this if response structure differs
          const decrypted = this.utility.decrypt(res.encryptedBody || res);
          // If there's some extra string after JSON, trim it
          const jsonEndIndex = decrypted.indexOf('}#') + 1;
          const jsonString = jsonEndIndex > 0 ? decrypted.substring(0, jsonEndIndex) : decrypted;

          const parsed = JSON.parse(jsonString);

          // Set authentication info
          AuthenticationHelper.setToken(parsed.accessToken);
          this.toastr.success(AppConstant.USER_LOGIN_SUCCESS);
          this.dashboardService._FirstName.emit(parsed.firstName);
          AuthenticationHelper.setUsername(parsed.firstName);
          AuthenticationHelper.setEmail(parsed.email);
          AuthenticationHelper.setRoleID(parsed.roleId);
          AuthenticationHelper.setRegionCode(parsed.regionCode);
          AuthenticationHelper.setTerritoryCode(parsed.territoryCode);
          AuthenticationHelper.setZoneCode(parsed.zoneCode);
          AuthenticationHelper.setUserID(parsed.userId);
          AuthenticationHelper.setProfitCenter(parsed.profitCenterCode);
          AuthenticationHelper.setProfitCenterName(parsed.profitCenterName);

          // Navigate based on role
          switch (parsed.roleId) {
            case 1:
            case 2:
            case 3:
            case 4:
              this.router.navigate(['dashboard']);
              break;
            case 5:
              this.router.navigate(['zones']);
              break;
            case 6:
              this.router.navigate(['sbu']);
              break;
            default:
              this.router.navigate(['dashboard']);
          }
        } catch (e) {
          this.toastr.error('Failed to process login response.');
        } finally {
          this.spinner.hide();
        }
      },
      error: (err: any) => {
        try {
          // Try to decrypt error response if possible
          const decryptedErr = this.utility.decrypt(err.error?.encryptedBody || err.error || err);
          const jsonEndIndex = decryptedErr.indexOf('}#') + 1;
          const errorString = jsonEndIndex > 0 ? decryptedErr.substring(0, jsonEndIndex) : decryptedErr;
          const parsedErr = JSON.parse(errorString);
          this.toastr.error(parsedErr.message || AppConstant.NETWORK_ERROR);
        } catch {
          this.toastr.error(AppConstant.NETWORK_ERROR);
        } finally {
          this.submitted = false;
          this.spinner.hide();
        }
      }
    });

    this.userService.fetchUserRole();
  }

  handleAuthentication(data: any): void {
    AuthenticationHelper.setUsername(data.firstName);
    AuthenticationHelper.setEmail(data.email);
    AuthenticationHelper.setRoleID(data.roleId);
    AuthenticationHelper.setUserID(data.id);
    localStorage.setItem('profileImage', data.image);
    AuthenticationHelper.setProfitCenter(data.profitCenterCode);
    AuthenticationHelper.setProfitCenterName(data.profitCenterName);
    this.toastr.success(AppConstant.USER_LOGIN_SUCCESS);
    this.router.navigate(['dashboard']);
  }

  errorHandler(err: any): void {
    this.spinner.show();
  }

  sanitizeInput(input: string): string {
    return input.replace(/<[^>]*>/g, '').replace(/[&^*#$!@()%]/g, '');
  }

  onInputChange(event: Event): void {
    const input = event.target as HTMLInputElement;
    input.value = this.sanitizeInput(input.value);
  }

  onPaste(event: ClipboardEvent): void {
    event.preventDefault();
    const text = event.clipboardData!.getData('text/plain');
    document.execCommand('insertText', false, this.sanitizeInput(text));
  }
}
